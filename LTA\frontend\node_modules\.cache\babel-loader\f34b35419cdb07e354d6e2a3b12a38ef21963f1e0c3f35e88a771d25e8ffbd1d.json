{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\LocationPicker.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Form, Button, Alert, Spinner, InputGroup, ListGroup } from 'react-bootstrap';\nimport { FaMapMarkerAlt, FaCrosshairs, FaTimes } from 'react-icons/fa';\nimport { getDeviceCoordinates } from '../utils/deviceCoordinates';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LocationPicker = ({\n  label,\n  placeholder,\n  onLocationSelect,\n  allowAutoDetect = true,\n  allowManualEntry = true,\n  initialLocation = null,\n  disabled = false\n}) => {\n  _s();\n  const [inputValue, setInputValue] = useState('');\n  const [selectedLocation, setSelectedLocation] = useState(initialLocation);\n  const [suggestions, setSuggestions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [autoDetecting, setAutoDetecting] = useState(false);\n  const [error, setError] = useState('');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const searchTimeoutRef = useRef(null);\n  const inputRef = useRef(null);\n  useEffect(() => {\n    if (initialLocation) {\n      setSelectedLocation(initialLocation);\n      setInputValue(initialLocation.address || `${initialLocation.latitude}, ${initialLocation.longitude}`);\n    }\n  }, [initialLocation]);\n\n  // Auto-detect current location\n  const handleAutoDetect = async () => {\n    if (!allowAutoDetect || disabled) return;\n    setAutoDetecting(true);\n    setError('');\n    try {\n      console.log('🌍 Starting auto-detection...');\n\n      // Get device coordinates with high accuracy\n      const coords = await getDeviceCoordinates({\n        enableHighAccuracy: true,\n        timeout: 15000,\n        maximumAge: 60000,\n        fallbackToIP: true\n      });\n      console.log('✅ Got coordinates:', coords);\n\n      // Reverse geocode to get address\n      const response = await fetch('/api/location/reverse-geocode', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          latitude: coords.latitude,\n          longitude: coords.longitude\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const location = {\n            latitude: coords.latitude,\n            longitude: coords.longitude,\n            address: data.result.address,\n            source: 'auto-detect',\n            accuracy: coords.accuracy\n          };\n          setSelectedLocation(location);\n          setInputValue(location.address);\n          onLocationSelect(location);\n          console.log('✅ Auto-detection successful:', location);\n        } else {\n          throw new Error(data.error || 'Reverse geocoding failed');\n        }\n      } else {\n        throw new Error('Failed to get address for location');\n      }\n    } catch (error) {\n      console.error('❌ Auto-detection failed:', error);\n      setError(`Auto-detection failed: ${error.message}`);\n\n      // Fallback: use coordinates without address\n      if (error.coords) {\n        const location = {\n          latitude: error.coords.latitude,\n          longitude: error.coords.longitude,\n          address: `${error.coords.latitude.toFixed(6)}, ${error.coords.longitude.toFixed(6)}`,\n          source: 'auto-detect-fallback',\n          accuracy: error.coords.accuracy\n        };\n        setSelectedLocation(location);\n        setInputValue(location.address);\n        onLocationSelect(location);\n      }\n    } finally {\n      setAutoDetecting(false);\n    }\n  };\n\n  // Handle manual input with geocoding\n  const handleInputChange = e => {\n    const value = e.target.value;\n    setInputValue(value);\n    setError('');\n\n    // Clear previous timeout\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n\n    // If input is empty, clear suggestions\n    if (!value.trim()) {\n      setSuggestions([]);\n      setShowSuggestions(false);\n      return;\n    }\n\n    // Debounce search\n    searchTimeoutRef.current = setTimeout(() => {\n      searchAddresses(value);\n    }, 500);\n  };\n\n  // Search for addresses using geocoding\n  const searchAddresses = async query => {\n    if (!allowManualEntry || !query.trim()) return;\n    setLoading(true);\n    try {\n      const response = await fetch('/api/location/geocode', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          address: query,\n          country_code: 'IN'\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          // For now, we get single result from our API\n          // In future, we could modify backend to return multiple results\n          setSuggestions([{\n            latitude: data.result.latitude,\n            longitude: data.result.longitude,\n            address: data.result.address,\n            source: data.result.source,\n            confidence: data.result.confidence\n          }]);\n          setShowSuggestions(true);\n        } else {\n          setSuggestions([]);\n          setShowSuggestions(false);\n        }\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setSuggestions([]);\n      setShowSuggestions(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle suggestion selection\n  const handleSuggestionSelect = suggestion => {\n    const location = {\n      latitude: suggestion.latitude,\n      longitude: suggestion.longitude,\n      address: suggestion.address,\n      source: 'manual-search',\n      confidence: suggestion.confidence\n    };\n    setSelectedLocation(location);\n    setInputValue(suggestion.address);\n    setSuggestions([]);\n    setShowSuggestions(false);\n    onLocationSelect(location);\n  };\n\n  // Clear selection\n  const handleClear = () => {\n    setSelectedLocation(null);\n    setInputValue('');\n    setSuggestions([]);\n    setShowSuggestions(false);\n    setError('');\n    onLocationSelect(null);\n  };\n\n  // Handle direct coordinate input (lat,lon format)\n  const handleCoordinateInput = () => {\n    const coordPattern = /^(-?\\d+\\.?\\d*),\\s*(-?\\d+\\.?\\d*)$/;\n    const match = inputValue.match(coordPattern);\n    if (match) {\n      const latitude = parseFloat(match[1]);\n      const longitude = parseFloat(match[2]);\n      if (latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180) {\n        const location = {\n          latitude,\n          longitude,\n          address: `${latitude}, ${longitude}`,\n          source: 'manual-coordinates'\n        };\n        setSelectedLocation(location);\n        onLocationSelect(location);\n        return true;\n      }\n    }\n    return false;\n  };\n\n  // Handle Enter key\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n\n      // Try coordinate input first\n      if (handleCoordinateInput()) {\n        return;\n      }\n\n      // If there are suggestions, select the first one\n      if (suggestions.length > 0) {\n        handleSuggestionSelect(suggestions[0]);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"location-picker\",\n    children: /*#__PURE__*/_jsxDEV(Form.Group, {\n      className: \"mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n          ref: inputRef,\n          type: \"text\",\n          placeholder: placeholder,\n          value: inputValue,\n          onChange: handleInputChange,\n          onKeyPress: handleKeyPress,\n          disabled: disabled || autoDetecting,\n          className: selectedLocation ? 'border-success' : ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), allowAutoDetect && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-primary\",\n          onClick: handleAutoDetect,\n          disabled: disabled || autoDetecting || loading,\n          title: \"Auto-detect current location\",\n          children: autoDetecting ? /*#__PURE__*/_jsxDEV(Spinner, {\n            animation: \"border\",\n            size: \"sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(FaCrosshairs, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this), selectedLocation && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          onClick: handleClear,\n          disabled: disabled,\n          title: \"Clear selection\",\n          children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), loading && !autoDetecting && /*#__PURE__*/_jsxDEV(InputGroup.Text, {\n          children: /*#__PURE__*/_jsxDEV(Spinner, {\n            animation: \"border\",\n            size: \"sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), showSuggestions && suggestions.length > 0 && /*#__PURE__*/_jsxDEV(ListGroup, {\n        className: \"mt-1 position-absolute\",\n        style: {\n          zIndex: 1000,\n          width: '100%'\n        },\n        children: suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n          action: true,\n          onClick: () => handleSuggestionSelect(suggestion),\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n            className: \"me-2 text-primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fw-bold\",\n              children: suggestion.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [suggestion.latitude.toFixed(6), \", \", suggestion.longitude.toFixed(6), suggestion.confidence && ` • Confidence: ${(suggestion.confidence * 100).toFixed(0)}%`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this), selectedLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-light rounded border\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n            className: \"me-2 text-success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-grow-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fw-bold text-success\",\n              children: selectedLocation.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [selectedLocation.latitude.toFixed(6), \", \", selectedLocation.longitude.toFixed(6), selectedLocation.accuracy && ` • Accuracy: ±${selectedLocation.accuracy.toFixed(0)}m`, selectedLocation.source && ` • Source: ${selectedLocation.source}`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"warning\",\n        className: \"mt-2 mb-0\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n        className: \"text-muted\",\n        children: [allowAutoDetect && allowManualEntry && \"Click the crosshairs to auto-detect your location, or type an address/coordinates (lat,lon)\", allowAutoDetect && !allowManualEntry && \"Click the crosshairs to auto-detect your location\", !allowAutoDetect && allowManualEntry && \"Enter an address or coordinates in lat,lon format\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 250,\n    columnNumber: 5\n  }, this);\n};\n_s(LocationPicker, \"mw2J3X5K55O+M/o8xzL3HB7z7Vs=\");\n_c = LocationPicker;\nexport default LocationPicker;\nvar _c;\n$RefreshReg$(_c, \"LocationPicker\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "InputGroup", "ListGroup", "FaMapMarkerAlt", "FaCrosshairs", "FaTimes", "getDeviceCoordinates", "jsxDEV", "_jsxDEV", "LocationPicker", "label", "placeholder", "onLocationSelect", "allowAutoDetect", "allowManualEntry", "initialLocation", "disabled", "_s", "inputValue", "setInputValue", "selectedLocation", "setSelectedLocation", "suggestions", "setSuggestions", "loading", "setLoading", "autoDetecting", "setAutoDetecting", "error", "setError", "showSuggestions", "setShowSuggestions", "searchTimeoutRef", "inputRef", "address", "latitude", "longitude", "handleAutoDetect", "console", "log", "coords", "enableHighAccuracy", "timeout", "maximumAge", "fallbackToIP", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "data", "json", "success", "location", "result", "source", "accuracy", "Error", "message", "toFixed", "handleInputChange", "e", "value", "target", "current", "clearTimeout", "trim", "setTimeout", "searchAddresses", "query", "country_code", "confidence", "handleSuggestionSelect", "suggestion", "handleClear", "handleCoordinateInput", "coordPattern", "match", "parseFloat", "handleKeyPress", "key", "preventDefault", "length", "className", "children", "Group", "Label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Control", "ref", "type", "onChange", "onKeyPress", "variant", "onClick", "title", "animation", "size", "Text", "style", "zIndex", "width", "map", "index", "<PERSON><PERSON>", "action", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/LocationPicker.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { <PERSON>, <PERSON><PERSON>, Alert, Spinner, InputGroup, ListGroup } from 'react-bootstrap';\nimport { FaMapMarkerAlt, FaCrosshairs, FaTimes } from 'react-icons/fa';\nimport { getDeviceCoordinates } from '../utils/deviceCoordinates';\n\nconst LocationPicker = ({ \n  label, \n  placeholder, \n  onLocationSelect, \n  allowAutoDetect = true, \n  allowManualEntry = true,\n  initialLocation = null,\n  disabled = false \n}) => {\n  const [inputValue, setInputValue] = useState('');\n  const [selectedLocation, setSelectedLocation] = useState(initialLocation);\n  const [suggestions, setSuggestions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [autoDetecting, setAutoDetecting] = useState(false);\n  const [error, setError] = useState('');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  \n  const searchTimeoutRef = useRef(null);\n  const inputRef = useRef(null);\n\n  useEffect(() => {\n    if (initialLocation) {\n      setSelectedLocation(initialLocation);\n      setInputValue(initialLocation.address || `${initialLocation.latitude}, ${initialLocation.longitude}`);\n    }\n  }, [initialLocation]);\n\n  // Auto-detect current location\n  const handleAutoDetect = async () => {\n    if (!allowAutoDetect || disabled) return;\n    \n    setAutoDetecting(true);\n    setError('');\n    \n    try {\n      console.log('🌍 Starting auto-detection...');\n      \n      // Get device coordinates with high accuracy\n      const coords = await getDeviceCoordinates({\n        enableHighAccuracy: true,\n        timeout: 15000,\n        maximumAge: 60000,\n        fallbackToIP: true\n      });\n      \n      console.log('✅ Got coordinates:', coords);\n      \n      // Reverse geocode to get address\n      const response = await fetch('/api/location/reverse-geocode', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          latitude: coords.latitude,\n          longitude: coords.longitude\n        })\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const location = {\n            latitude: coords.latitude,\n            longitude: coords.longitude,\n            address: data.result.address,\n            source: 'auto-detect',\n            accuracy: coords.accuracy\n          };\n          \n          setSelectedLocation(location);\n          setInputValue(location.address);\n          onLocationSelect(location);\n          \n          console.log('✅ Auto-detection successful:', location);\n        } else {\n          throw new Error(data.error || 'Reverse geocoding failed');\n        }\n      } else {\n        throw new Error('Failed to get address for location');\n      }\n      \n    } catch (error) {\n      console.error('❌ Auto-detection failed:', error);\n      setError(`Auto-detection failed: ${error.message}`);\n      \n      // Fallback: use coordinates without address\n      if (error.coords) {\n        const location = {\n          latitude: error.coords.latitude,\n          longitude: error.coords.longitude,\n          address: `${error.coords.latitude.toFixed(6)}, ${error.coords.longitude.toFixed(6)}`,\n          source: 'auto-detect-fallback',\n          accuracy: error.coords.accuracy\n        };\n        \n        setSelectedLocation(location);\n        setInputValue(location.address);\n        onLocationSelect(location);\n      }\n    } finally {\n      setAutoDetecting(false);\n    }\n  };\n\n  // Handle manual input with geocoding\n  const handleInputChange = (e) => {\n    const value = e.target.value;\n    setInputValue(value);\n    setError('');\n    \n    // Clear previous timeout\n    if (searchTimeoutRef.current) {\n      clearTimeout(searchTimeoutRef.current);\n    }\n    \n    // If input is empty, clear suggestions\n    if (!value.trim()) {\n      setSuggestions([]);\n      setShowSuggestions(false);\n      return;\n    }\n    \n    // Debounce search\n    searchTimeoutRef.current = setTimeout(() => {\n      searchAddresses(value);\n    }, 500);\n  };\n\n  // Search for addresses using geocoding\n  const searchAddresses = async (query) => {\n    if (!allowManualEntry || !query.trim()) return;\n    \n    setLoading(true);\n    \n    try {\n      const response = await fetch('/api/location/geocode', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          address: query,\n          country_code: 'IN'\n        })\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          // For now, we get single result from our API\n          // In future, we could modify backend to return multiple results\n          setSuggestions([{\n            latitude: data.result.latitude,\n            longitude: data.result.longitude,\n            address: data.result.address,\n            source: data.result.source,\n            confidence: data.result.confidence\n          }]);\n          setShowSuggestions(true);\n        } else {\n          setSuggestions([]);\n          setShowSuggestions(false);\n        }\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setSuggestions([]);\n      setShowSuggestions(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle suggestion selection\n  const handleSuggestionSelect = (suggestion) => {\n    const location = {\n      latitude: suggestion.latitude,\n      longitude: suggestion.longitude,\n      address: suggestion.address,\n      source: 'manual-search',\n      confidence: suggestion.confidence\n    };\n    \n    setSelectedLocation(location);\n    setInputValue(suggestion.address);\n    setSuggestions([]);\n    setShowSuggestions(false);\n    onLocationSelect(location);\n  };\n\n  // Clear selection\n  const handleClear = () => {\n    setSelectedLocation(null);\n    setInputValue('');\n    setSuggestions([]);\n    setShowSuggestions(false);\n    setError('');\n    onLocationSelect(null);\n  };\n\n  // Handle direct coordinate input (lat,lon format)\n  const handleCoordinateInput = () => {\n    const coordPattern = /^(-?\\d+\\.?\\d*),\\s*(-?\\d+\\.?\\d*)$/;\n    const match = inputValue.match(coordPattern);\n    \n    if (match) {\n      const latitude = parseFloat(match[1]);\n      const longitude = parseFloat(match[2]);\n      \n      if (latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180) {\n        const location = {\n          latitude,\n          longitude,\n          address: `${latitude}, ${longitude}`,\n          source: 'manual-coordinates'\n        };\n        \n        setSelectedLocation(location);\n        onLocationSelect(location);\n        return true;\n      }\n    }\n    return false;\n  };\n\n  // Handle Enter key\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      \n      // Try coordinate input first\n      if (handleCoordinateInput()) {\n        return;\n      }\n      \n      // If there are suggestions, select the first one\n      if (suggestions.length > 0) {\n        handleSuggestionSelect(suggestions[0]);\n      }\n    }\n  };\n\n  return (\n    <div className=\"location-picker\">\n      <Form.Group className=\"mb-3\">\n        <Form.Label>{label}</Form.Label>\n        \n        <InputGroup>\n          <Form.Control\n            ref={inputRef}\n            type=\"text\"\n            placeholder={placeholder}\n            value={inputValue}\n            onChange={handleInputChange}\n            onKeyPress={handleKeyPress}\n            disabled={disabled || autoDetecting}\n            className={selectedLocation ? 'border-success' : ''}\n          />\n          \n          {allowAutoDetect && (\n            <Button\n              variant=\"outline-primary\"\n              onClick={handleAutoDetect}\n              disabled={disabled || autoDetecting || loading}\n              title=\"Auto-detect current location\"\n            >\n              {autoDetecting ? (\n                <Spinner animation=\"border\" size=\"sm\" />\n              ) : (\n                <FaCrosshairs />\n              )}\n            </Button>\n          )}\n          \n          {selectedLocation && (\n            <Button\n              variant=\"outline-secondary\"\n              onClick={handleClear}\n              disabled={disabled}\n              title=\"Clear selection\"\n            >\n              <FaTimes />\n            </Button>\n          )}\n          \n          {loading && !autoDetecting && (\n            <InputGroup.Text>\n              <Spinner animation=\"border\" size=\"sm\" />\n            </InputGroup.Text>\n          )}\n        </InputGroup>\n        \n        {/* Suggestions dropdown */}\n        {showSuggestions && suggestions.length > 0 && (\n          <ListGroup className=\"mt-1 position-absolute\" style={{ zIndex: 1000, width: '100%' }}>\n            {suggestions.map((suggestion, index) => (\n              <ListGroup.Item\n                key={index}\n                action\n                onClick={() => handleSuggestionSelect(suggestion)}\n                className=\"d-flex align-items-center\"\n              >\n                <FaMapMarkerAlt className=\"me-2 text-primary\" />\n                <div>\n                  <div className=\"fw-bold\">{suggestion.address}</div>\n                  <small className=\"text-muted\">\n                    {suggestion.latitude.toFixed(6)}, {suggestion.longitude.toFixed(6)}\n                    {suggestion.confidence && ` • Confidence: ${(suggestion.confidence * 100).toFixed(0)}%`}\n                  </small>\n                </div>\n              </ListGroup.Item>\n            ))}\n          </ListGroup>\n        )}\n        \n        {/* Selected location display */}\n        {selectedLocation && (\n          <div className=\"mt-2 p-2 bg-light rounded border\">\n            <div className=\"d-flex align-items-center\">\n              <FaMapMarkerAlt className=\"me-2 text-success\" />\n              <div className=\"flex-grow-1\">\n                <div className=\"fw-bold text-success\">{selectedLocation.address}</div>\n                <small className=\"text-muted\">\n                  {selectedLocation.latitude.toFixed(6)}, {selectedLocation.longitude.toFixed(6)}\n                  {selectedLocation.accuracy && ` • Accuracy: ±${selectedLocation.accuracy.toFixed(0)}m`}\n                  {selectedLocation.source && ` • Source: ${selectedLocation.source}`}\n                </small>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        {/* Error display */}\n        {error && (\n          <Alert variant=\"warning\" className=\"mt-2 mb-0\">\n            {error}\n          </Alert>\n        )}\n        \n        {/* Help text */}\n        <Form.Text className=\"text-muted\">\n          {allowAutoDetect && allowManualEntry && \n            \"Click the crosshairs to auto-detect your location, or type an address/coordinates (lat,lon)\"}\n          {allowAutoDetect && !allowManualEntry && \n            \"Click the crosshairs to auto-detect your location\"}\n          {!allowAutoDetect && allowManualEntry && \n            \"Enter an address or coordinates in lat,lon format\"}\n        </Form.Text>\n      </Form.Group>\n    </div>\n  );\n};\n\nexport default LocationPicker;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,QAAQ,iBAAiB;AACrF,SAASC,cAAc,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AACtE,SAASC,oBAAoB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAc,GAAGA,CAAC;EACtBC,KAAK;EACLC,WAAW;EACXC,gBAAgB;EAChBC,eAAe,GAAG,IAAI;EACtBC,gBAAgB,GAAG,IAAI;EACvBC,eAAe,GAAG,IAAI;EACtBC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAACqB,eAAe,CAAC;EACzE,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMsC,gBAAgB,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMqC,QAAQ,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAE7BD,SAAS,CAAC,MAAM;IACd,IAAIoB,eAAe,EAAE;MACnBM,mBAAmB,CAACN,eAAe,CAAC;MACpCI,aAAa,CAACJ,eAAe,CAACmB,OAAO,IAAI,GAAGnB,eAAe,CAACoB,QAAQ,KAAKpB,eAAe,CAACqB,SAAS,EAAE,CAAC;IACvG;EACF,CAAC,EAAE,CAACrB,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMsB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACxB,eAAe,IAAIG,QAAQ,EAAE;IAElCW,gBAAgB,CAAC,IAAI,CAAC;IACtBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACFS,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,MAAMC,MAAM,GAAG,MAAMlC,oBAAoB,CAAC;QACxCmC,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,MAAM,CAAC;;MAEzC;MACA,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,EAAE;QAC5DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBhB,QAAQ,EAAEK,MAAM,CAACL,QAAQ;UACzBC,SAAS,EAAEI,MAAM,CAACJ;QACpB,CAAC;MACH,CAAC,CAAC;MAEF,IAAIS,QAAQ,CAACO,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,MAAMC,QAAQ,GAAG;YACfrB,QAAQ,EAAEK,MAAM,CAACL,QAAQ;YACzBC,SAAS,EAAEI,MAAM,CAACJ,SAAS;YAC3BF,OAAO,EAAEmB,IAAI,CAACI,MAAM,CAACvB,OAAO;YAC5BwB,MAAM,EAAE,aAAa;YACrBC,QAAQ,EAAEnB,MAAM,CAACmB;UACnB,CAAC;UAEDtC,mBAAmB,CAACmC,QAAQ,CAAC;UAC7BrC,aAAa,CAACqC,QAAQ,CAACtB,OAAO,CAAC;UAC/BtB,gBAAgB,CAAC4C,QAAQ,CAAC;UAE1BlB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiB,QAAQ,CAAC;QACvD,CAAC,MAAM;UACL,MAAM,IAAII,KAAK,CAACP,IAAI,CAACzB,KAAK,IAAI,0BAA0B,CAAC;QAC3D;MACF,CAAC,MAAM;QACL,MAAM,IAAIgC,KAAK,CAAC,oCAAoC,CAAC;MACvD;IAEF,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0BAA0BD,KAAK,CAACiC,OAAO,EAAE,CAAC;;MAEnD;MACA,IAAIjC,KAAK,CAACY,MAAM,EAAE;QAChB,MAAMgB,QAAQ,GAAG;UACfrB,QAAQ,EAAEP,KAAK,CAACY,MAAM,CAACL,QAAQ;UAC/BC,SAAS,EAAER,KAAK,CAACY,MAAM,CAACJ,SAAS;UACjCF,OAAO,EAAE,GAAGN,KAAK,CAACY,MAAM,CAACL,QAAQ,CAAC2B,OAAO,CAAC,CAAC,CAAC,KAAKlC,KAAK,CAACY,MAAM,CAACJ,SAAS,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAE;UACpFJ,MAAM,EAAE,sBAAsB;UAC9BC,QAAQ,EAAE/B,KAAK,CAACY,MAAM,CAACmB;QACzB,CAAC;QAEDtC,mBAAmB,CAACmC,QAAQ,CAAC;QAC7BrC,aAAa,CAACqC,QAAQ,CAACtB,OAAO,CAAC;QAC/BtB,gBAAgB,CAAC4C,QAAQ,CAAC;MAC5B;IACF,CAAC,SAAS;MACR7B,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACD,KAAK;IAC5B9C,aAAa,CAAC8C,KAAK,CAAC;IACpBpC,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIG,gBAAgB,CAACmC,OAAO,EAAE;MAC5BC,YAAY,CAACpC,gBAAgB,CAACmC,OAAO,CAAC;IACxC;;IAEA;IACA,IAAI,CAACF,KAAK,CAACI,IAAI,CAAC,CAAC,EAAE;MACjB9C,cAAc,CAAC,EAAE,CAAC;MAClBQ,kBAAkB,CAAC,KAAK,CAAC;MACzB;IACF;;IAEA;IACAC,gBAAgB,CAACmC,OAAO,GAAGG,UAAU,CAAC,MAAM;MAC1CC,eAAe,CAACN,KAAK,CAAC;IACxB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMM,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI,CAAC1D,gBAAgB,IAAI,CAAC0D,KAAK,CAACH,IAAI,CAAC,CAAC,EAAE;IAExC5C,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAAC,uBAAuB,EAAE;QACpDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBjB,OAAO,EAAEsC,KAAK;UACdC,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI5B,QAAQ,CAACO,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB;UACA;UACAhC,cAAc,CAAC,CAAC;YACdY,QAAQ,EAAEkB,IAAI,CAACI,MAAM,CAACtB,QAAQ;YAC9BC,SAAS,EAAEiB,IAAI,CAACI,MAAM,CAACrB,SAAS;YAChCF,OAAO,EAAEmB,IAAI,CAACI,MAAM,CAACvB,OAAO;YAC5BwB,MAAM,EAAEL,IAAI,CAACI,MAAM,CAACC,MAAM;YAC1BgB,UAAU,EAAErB,IAAI,CAACI,MAAM,CAACiB;UAC1B,CAAC,CAAC,CAAC;UACH3C,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAC,MAAM;UACLR,cAAc,CAAC,EAAE,CAAC;UAClBQ,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCL,cAAc,CAAC,EAAE,CAAC;MAClBQ,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkD,sBAAsB,GAAIC,UAAU,IAAK;IAC7C,MAAMpB,QAAQ,GAAG;MACfrB,QAAQ,EAAEyC,UAAU,CAACzC,QAAQ;MAC7BC,SAAS,EAAEwC,UAAU,CAACxC,SAAS;MAC/BF,OAAO,EAAE0C,UAAU,CAAC1C,OAAO;MAC3BwB,MAAM,EAAE,eAAe;MACvBgB,UAAU,EAAEE,UAAU,CAACF;IACzB,CAAC;IAEDrD,mBAAmB,CAACmC,QAAQ,CAAC;IAC7BrC,aAAa,CAACyD,UAAU,CAAC1C,OAAO,CAAC;IACjCX,cAAc,CAAC,EAAE,CAAC;IAClBQ,kBAAkB,CAAC,KAAK,CAAC;IACzBnB,gBAAgB,CAAC4C,QAAQ,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqB,WAAW,GAAGA,CAAA,KAAM;IACxBxD,mBAAmB,CAAC,IAAI,CAAC;IACzBF,aAAa,CAAC,EAAE,CAAC;IACjBI,cAAc,CAAC,EAAE,CAAC;IAClBQ,kBAAkB,CAAC,KAAK,CAAC;IACzBF,QAAQ,CAAC,EAAE,CAAC;IACZjB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMkE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,YAAY,GAAG,kCAAkC;IACvD,MAAMC,KAAK,GAAG9D,UAAU,CAAC8D,KAAK,CAACD,YAAY,CAAC;IAE5C,IAAIC,KAAK,EAAE;MACT,MAAM7C,QAAQ,GAAG8C,UAAU,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MACrC,MAAM5C,SAAS,GAAG6C,UAAU,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MAEtC,IAAI7C,QAAQ,IAAI,CAAC,EAAE,IAAIA,QAAQ,IAAI,EAAE,IAAIC,SAAS,IAAI,CAAC,GAAG,IAAIA,SAAS,IAAI,GAAG,EAAE;QAC9E,MAAMoB,QAAQ,GAAG;UACfrB,QAAQ;UACRC,SAAS;UACTF,OAAO,EAAE,GAAGC,QAAQ,KAAKC,SAAS,EAAE;UACpCsB,MAAM,EAAE;QACV,CAAC;QAEDrC,mBAAmB,CAACmC,QAAQ,CAAC;QAC7B5C,gBAAgB,CAAC4C,QAAQ,CAAC;QAC1B,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAM0B,cAAc,GAAIlB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACmB,GAAG,KAAK,OAAO,EAAE;MACrBnB,CAAC,CAACoB,cAAc,CAAC,CAAC;;MAElB;MACA,IAAIN,qBAAqB,CAAC,CAAC,EAAE;QAC3B;MACF;;MAEA;MACA,IAAIxD,WAAW,CAAC+D,MAAM,GAAG,CAAC,EAAE;QAC1BV,sBAAsB,CAACrD,WAAW,CAAC,CAAC,CAAC,CAAC;MACxC;IACF;EACF,CAAC;EAED,oBACEd,OAAA;IAAK8E,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9B/E,OAAA,CAACX,IAAI,CAAC2F,KAAK;MAACF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAC1B/E,OAAA,CAACX,IAAI,CAAC4F,KAAK;QAAAF,QAAA,EAAE7E;MAAK;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAEhCrF,OAAA,CAACP,UAAU;QAAAsF,QAAA,gBACT/E,OAAA,CAACX,IAAI,CAACiG,OAAO;UACXC,GAAG,EAAE9D,QAAS;UACd+D,IAAI,EAAC,MAAM;UACXrF,WAAW,EAAEA,WAAY;UACzBsD,KAAK,EAAE/C,UAAW;UAClB+E,QAAQ,EAAElC,iBAAkB;UAC5BmC,UAAU,EAAEhB,cAAe;UAC3BlE,QAAQ,EAAEA,QAAQ,IAAIU,aAAc;UACpC4D,SAAS,EAAElE,gBAAgB,GAAG,gBAAgB,GAAG;QAAG;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EAEDhF,eAAe,iBACdL,OAAA,CAACV,MAAM;UACLqG,OAAO,EAAC,iBAAiB;UACzBC,OAAO,EAAE/D,gBAAiB;UAC1BrB,QAAQ,EAAEA,QAAQ,IAAIU,aAAa,IAAIF,OAAQ;UAC/C6E,KAAK,EAAC,8BAA8B;UAAAd,QAAA,EAEnC7D,aAAa,gBACZlB,OAAA,CAACR,OAAO;YAACsG,SAAS,EAAC,QAAQ;YAACC,IAAI,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAExCrF,OAAA,CAACJ,YAAY;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAChB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CACT,EAEAzE,gBAAgB,iBACfZ,OAAA,CAACV,MAAM;UACLqG,OAAO,EAAC,mBAAmB;UAC3BC,OAAO,EAAEvB,WAAY;UACrB7D,QAAQ,EAAEA,QAAS;UACnBqF,KAAK,EAAC,iBAAiB;UAAAd,QAAA,eAEvB/E,OAAA,CAACH,OAAO;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT,EAEArE,OAAO,IAAI,CAACE,aAAa,iBACxBlB,OAAA,CAACP,UAAU,CAACuG,IAAI;UAAAjB,QAAA,eACd/E,OAAA,CAACR,OAAO;YAACsG,SAAS,EAAC,QAAQ;YAACC,IAAI,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ/D,eAAe,IAAIR,WAAW,CAAC+D,MAAM,GAAG,CAAC,iBACxC7E,OAAA,CAACN,SAAS;QAACoF,SAAS,EAAC,wBAAwB;QAACmB,KAAK,EAAE;UAAEC,MAAM,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAApB,QAAA,EAClFjE,WAAW,CAACsF,GAAG,CAAC,CAAChC,UAAU,EAAEiC,KAAK,kBACjCrG,OAAA,CAACN,SAAS,CAAC4G,IAAI;UAEbC,MAAM;UACNX,OAAO,EAAEA,CAAA,KAAMzB,sBAAsB,CAACC,UAAU,CAAE;UAClDU,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAErC/E,OAAA,CAACL,cAAc;YAACmF,SAAS,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDrF,OAAA;YAAA+E,QAAA,gBACE/E,OAAA;cAAK8E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEX,UAAU,CAAC1C;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDrF,OAAA;cAAO8E,SAAS,EAAC,YAAY;cAAAC,QAAA,GAC1BX,UAAU,CAACzC,QAAQ,CAAC2B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACc,UAAU,CAACxC,SAAS,CAAC0B,OAAO,CAAC,CAAC,CAAC,EACjEc,UAAU,CAACF,UAAU,IAAI,kBAAkB,CAACE,UAAU,CAACF,UAAU,GAAG,GAAG,EAAEZ,OAAO,CAAC,CAAC,CAAC,GAAG;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GAZDgB,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaI,CACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CACZ,EAGAzE,gBAAgB,iBACfZ,OAAA;QAAK8E,SAAS,EAAC,kCAAkC;QAAAC,QAAA,eAC/C/E,OAAA;UAAK8E,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC/E,OAAA,CAACL,cAAc;YAACmF,SAAS,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDrF,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/E,OAAA;cAAK8E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAEnE,gBAAgB,CAACc;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtErF,OAAA;cAAO8E,SAAS,EAAC,YAAY;cAAAC,QAAA,GAC1BnE,gBAAgB,CAACe,QAAQ,CAAC2B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAC1C,gBAAgB,CAACgB,SAAS,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAC7E1C,gBAAgB,CAACuC,QAAQ,IAAI,iBAAiBvC,gBAAgB,CAACuC,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG,EACrF1C,gBAAgB,CAACsC,MAAM,IAAI,cAActC,gBAAgB,CAACsC,MAAM,EAAE;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjE,KAAK,iBACJpB,OAAA,CAACT,KAAK;QAACoG,OAAO,EAAC,SAAS;QAACb,SAAS,EAAC,WAAW;QAAAC,QAAA,EAC3C3D;MAAK;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAGDrF,OAAA,CAACX,IAAI,CAAC2G,IAAI;QAAClB,SAAS,EAAC,YAAY;QAAAC,QAAA,GAC9B1E,eAAe,IAAIC,gBAAgB,IAClC,6FAA6F,EAC9FD,eAAe,IAAI,CAACC,gBAAgB,IACnC,mDAAmD,EACpD,CAACD,eAAe,IAAIC,gBAAgB,IACnC,mDAAmD;MAAA;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAhWIR,cAAc;AAAAuG,EAAA,GAAdvG,cAAc;AAkWpB,eAAeA,cAAc;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}