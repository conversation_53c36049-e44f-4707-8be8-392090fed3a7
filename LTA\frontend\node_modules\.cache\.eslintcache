[{"C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js": "8", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js": "9", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js": "10", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js": "11", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js": "12", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js": "13", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js": "14", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js": "15", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js": "16", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js": "17", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\utils\\fileValidation.js": "18", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\RoutePlanner.js": "19", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\LiveTracking.js": "20", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\LocationPicker.js": "21", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\RouteMap.js": "22", "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\utils\\deviceCoordinates.js": "23"}, {"size": 633, "mtime": 1753938470359, "results": "24", "hashOfConfig": "25"}, {"size": 376, "mtime": 1753938470372, "results": "26", "hashOfConfig": "25"}, {"size": 3053, "mtime": 1757765143404, "results": "27", "hashOfConfig": "25"}, {"size": 4500, "mtime": 1757765294622, "results": "28", "hashOfConfig": "25"}, {"size": 2994, "mtime": 1753938470363, "results": "29", "hashOfConfig": "25"}, {"size": 38727, "mtime": 1757912487000, "results": "30", "hashOfConfig": "25"}, {"size": 27373, "mtime": 1753938470368, "results": "31", "hashOfConfig": "25"}, {"size": 94726, "mtime": 1758089786950, "results": "32", "hashOfConfig": "25"}, {"size": 35679, "mtime": 1758623648610, "results": "33", "hashOfConfig": "25"}, {"size": 69043, "mtime": 1758607982308, "results": "34", "hashOfConfig": "25"}, {"size": 659, "mtime": 1753938470351, "results": "35", "hashOfConfig": "25"}, {"size": 4656, "mtime": 1755329373591, "results": "36", "hashOfConfig": "25"}, {"size": 4005, "mtime": 1753938470352, "results": "37", "hashOfConfig": "25"}, {"size": 3414, "mtime": 1753938470350, "results": "38", "hashOfConfig": "25"}, {"size": 38428, "mtime": 1758706983661, "results": "39", "hashOfConfig": "25"}, {"size": 44480, "mtime": 1758610826460, "results": "40", "hashOfConfig": "25"}, {"size": 1018, "mtime": 1753938470357, "results": "41", "hashOfConfig": "25"}, {"size": 8812, "mtime": 1757912331715, "results": "42", "hashOfConfig": "25"}, {"size": 24053, "mtime": 1758706815900, "results": "43", "hashOfConfig": "25"}, {"size": 14273, "mtime": 1758698022242, "results": "44", "hashOfConfig": "25"}, {"size": 11152, "mtime": 1758697695215, "results": "45", "hashOfConfig": "25"}, {"size": 12034, "mtime": 1758704609731, "results": "46", "hashOfConfig": "25"}, {"size": 9005, "mtime": 1758706978897, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1djdb2s", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Login.js", ["117", "118", "119", "120"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\RoadInfrastructure.js", ["121", "122", "123", "124", "125", "126", "127"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Recommendation.js", ["128", "129", "130", "131", "132", "133", "134", "135", "136", "137"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Pavement.js", ["138", "139", "140", "141", "142", "143", "144", "145", "146", "147"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\DefectDetail.js", ["148", "149"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\pages\\Dashboard.js", ["150", "151"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\Sidebar.js", ["152", "153"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ResponsiveCheckboxList.js", ["154"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\ChartContainer.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\VideoDefectDetection.js", ["155", "156", "157", "158", "159", "160", "161", "162", "163"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\DefectMap.js", ["164", "165", "166"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\hooks\\useResponsive.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\utils\\fileValidation.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\RoutePlanner.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\LiveTracking.js", ["167", "168"], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\LocationPicker.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\components\\RouteMap.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LTAGIT\\LTA\\frontend\\src\\utils\\deviceCoordinates.js", ["169"], [], {"ruleId": "170", "severity": 1, "message": "171", "line": 3, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 19}, {"ruleId": "170", "severity": 1, "message": "174", "line": 3, "column": 21, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 24}, {"ruleId": "170", "severity": 1, "message": "175", "line": 3, "column": 26, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 29}, {"ruleId": "176", "severity": 1, "message": "177", "line": 52, "column": 29, "nodeType": "178", "messageId": "179", "endLine": 52, "endColumn": 78}, {"ruleId": "170", "severity": 1, "message": "180", "line": 4, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 4, "endColumn": 22}, {"ruleId": "170", "severity": 1, "message": "181", "line": 4, "column": 24, "nodeType": "172", "messageId": "173", "endLine": 4, "endColumn": 33}, {"ruleId": "170", "severity": 1, "message": "182", "line": 4, "column": 35, "nodeType": "172", "messageId": "173", "endLine": 4, "endColumn": 41}, {"ruleId": "170", "severity": 1, "message": "183", "line": 4, "column": 43, "nodeType": "172", "messageId": "173", "endLine": 4, "endColumn": 48}, {"ruleId": "170", "severity": 1, "message": "184", "line": 24, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 24, "endColumn": 19}, {"ruleId": "170", "severity": 1, "message": "185", "line": 71, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 71, "endColumn": 25}, {"ruleId": "186", "severity": 1, "message": "187", "line": 555, "column": 19, "nodeType": "188", "endLine": 559, "endColumn": 21}, {"ruleId": "170", "severity": 1, "message": "189", "line": 3, "column": 58, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 63}, {"ruleId": "170", "severity": 1, "message": "190", "line": 3, "column": 65, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 69}, {"ruleId": "170", "severity": 1, "message": "191", "line": 3, "column": 71, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 74}, {"ruleId": "170", "severity": 1, "message": "192", "line": 4, "column": 8, "nodeType": "172", "messageId": "173", "endLine": 4, "endColumn": 12}, {"ruleId": "170", "severity": 1, "message": "193", "line": 76, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 76, "endColumn": 25}, {"ruleId": "170", "severity": 1, "message": "194", "line": 79, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 79, "endColumn": 19}, {"ruleId": "170", "severity": 1, "message": "195", "line": 277, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 277, "endColumn": 22}, {"ruleId": "170", "severity": 1, "message": "196", "line": 291, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 291, "endColumn": 21}, {"ruleId": "170", "severity": 1, "message": "197", "line": 305, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 305, "endColumn": 30}, {"ruleId": "170", "severity": 1, "message": "198", "line": 318, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 318, "endColumn": 28}, {"ruleId": "170", "severity": 1, "message": "199", "line": 19, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 19, "endColumn": 24}, {"ruleId": "170", "severity": 1, "message": "200", "line": 20, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 20, "endColumn": 17}, {"ruleId": "170", "severity": 1, "message": "201", "line": 39, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 39, "endColumn": 33}, {"ruleId": "170", "severity": 1, "message": "202", "line": 40, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 40, "endColumn": 29}, {"ruleId": "203", "severity": 1, "message": "204", "line": 886, "column": 6, "nodeType": "205", "endLine": 886, "endColumn": 33, "suggestions": "206"}, {"ruleId": "203", "severity": 1, "message": "207", "line": 895, "column": 6, "nodeType": "205", "endLine": 895, "endColumn": 20, "suggestions": "208"}, {"ruleId": "203", "severity": 1, "message": "209", "line": 932, "column": 6, "nodeType": "205", "endLine": 932, "endColumn": 33, "suggestions": "210"}, {"ruleId": "186", "severity": 1, "message": "187", "line": 978, "column": 21, "nodeType": "188", "endLine": 982, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "187", "line": 2113, "column": 17, "nodeType": "188", "endLine": 2123, "endColumn": 19}, {"ruleId": "186", "severity": 1, "message": "187", "line": 2131, "column": 19, "nodeType": "188", "endLine": 2141, "endColumn": 21}, {"ruleId": "186", "severity": 1, "message": "187", "line": 187, "column": 9, "nodeType": "188", "endLine": 197, "endColumn": 11}, {"ruleId": "170", "severity": 1, "message": "211", "line": 247, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 247, "endColumn": 24}, {"ruleId": "170", "severity": 1, "message": "192", "line": 4, "column": 8, "nodeType": "172", "messageId": "173", "endLine": 4, "endColumn": 12}, {"ruleId": "203", "severity": 1, "message": "212", "line": 782, "column": 6, "nodeType": "205", "endLine": 782, "endColumn": 8, "suggestions": "213"}, {"ruleId": "170", "severity": 1, "message": "214", "line": 18, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 18, "endColumn": 20}, {"ruleId": "215", "severity": 1, "message": "216", "line": 93, "column": 11, "nodeType": "188", "endLine": 93, "endColumn": 15}, {"ruleId": "170", "severity": 1, "message": "217", "line": 35, "column": 11, "nodeType": "172", "messageId": "173", "endLine": 35, "endColumn": 16}, {"ruleId": "170", "severity": 1, "message": "218", "line": 13, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 13, "endColumn": 24}, {"ruleId": "170", "severity": 1, "message": "219", "line": 17, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 17, "endColumn": 20}, {"ruleId": "170", "severity": 1, "message": "220", "line": 24, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 24, "endColumn": 24}, {"ruleId": "170", "severity": 1, "message": "221", "line": 37, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 37, "endColumn": 27}, {"ruleId": "170", "severity": 1, "message": "222", "line": 47, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 47, "endColumn": 20}, {"ruleId": "203", "severity": 1, "message": "223", "line": 104, "column": 6, "nodeType": "205", "endLine": 104, "endColumn": 19, "suggestions": "224"}, {"ruleId": "170", "severity": 1, "message": "225", "line": 559, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 559, "endColumn": 24}, {"ruleId": "170", "severity": 1, "message": "226", "line": 560, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 560, "endColumn": 21}, {"ruleId": "170", "severity": 1, "message": "227", "line": 561, "column": 9, "nodeType": "172", "messageId": "173", "endLine": 561, "endColumn": 22}, {"ruleId": "186", "severity": 1, "message": "187", "line": 328, "column": 11, "nodeType": "188", "endLine": 340, "endColumn": 13}, {"ruleId": "203", "severity": 1, "message": "228", "line": 585, "column": 6, "nodeType": "205", "endLine": 585, "endColumn": 12, "suggestions": "229"}, {"ruleId": "203", "severity": 1, "message": "230", "line": 598, "column": 6, "nodeType": "205", "endLine": 598, "endColumn": 52, "suggestions": "231"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 94, "column": 41, "nodeType": "172", "messageId": "234", "endLine": 94, "endColumn": 61}, {"ruleId": "232", "severity": 1, "message": "235", "line": 94, "column": 63, "nodeType": "172", "messageId": "234", "endLine": 94, "endColumn": 82}, {"ruleId": "236", "severity": 1, "message": "237", "line": 62, "column": 9, "nodeType": "238", "messageId": "239", "endLine": 72, "endColumn": 10}, "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'Row' is defined but never used.", "'Col' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'retryCount'.", "ArrowFunctionExpression", "unsafeRefs", "'MapContainer' is defined but never used.", "'TileLayer' is defined but never used.", "'Marker' is defined but never used.", "'Popup' is defined but never used.", "'imageFile' is assigned a value but never used.", "'roadInfraClasses' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Badge' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Plot' is defined but never used.", "'recommendations' is assigned a value but never used.", "'chartData' is assigned a value but never used.", "'handleApprove' is assigned a value but never used.", "'handleReject' is assigned a value but never used.", "'getPriorityBadgeClass' is assigned a value but never used.", "'getStatusBadgeClass' is assigned a value but never used.", "'processedImage' is assigned a value but never used.", "'results' is assigned a value but never used.", "'showClassificationModal' is assigned a value but never used.", "'classificationError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'batchResults.length'. Either include it or remove the dependency array.", "ArrayExpression", ["240"], "React Hook useEffect has missing dependencies: 'handleLocationRequest' and 'locationPermission'. Either include them or remove the dependency array.", ["241"], "React Hook useEffect has a missing dependency: 'handleLocationRequest'. Either include it or remove the dependency array.", ["242"], "'toggleImageType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["243"], "'activePage' is assigned a value but never used.", "jsx-a11y/heading-has-content", "Headings must have content and the content must be accessible by a screen reader.", "'value' is assigned a value but never used.", "'processedVideo' is assigned a value but never used.", "'shouldStop' is assigned a value but never used.", "'recordedChunks' is assigned a value but never used.", "'currentDetections' is assigned a value but never used.", "'BUFFER_SIZE' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleStopRecording'. Either include it or remove the dependency array.", ["244"], "'handlePlayPause' is assigned a value but never used.", "'handleRewind' is assigned a value but never used.", "'handleForward' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchDefectData' and 'fetchUsers'. Either include them or remove the dependency array.", ["245"], "React Hook useEffect has a missing dependency: 'fetchDefectData'. Either include it or remove the dependency array.", ["246"], "no-use-before-define", "'handleLocationUpdate' was used before it was defined.", "usedBeforeDefined", "'updateTrackingStats' was used before it was defined.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", {"desc": "247", "fix": "248"}, {"desc": "249", "fix": "250"}, {"desc": "251", "fix": "252"}, {"desc": "253", "fix": "254"}, {"desc": "255", "fix": "256"}, {"desc": "257", "fix": "258"}, {"desc": "259", "fix": "260"}, "Update the dependencies array to be: [batchResults.length, roadClassificationEnabled]", {"range": "261", "text": "262"}, "Update the dependencies array to be: [cameraActive, handleLocationRequest, locationPermission]", {"range": "263", "text": "264"}, "Update the dependencies array to be: [cameraActive, coordinates, handleLocationRequest]", {"range": "265", "text": "266"}, "Update the dependencies array to be: [fetchData]", {"range": "267", "text": "268"}, "Update the dependencies array to be: [handleStopRecording, isRecording]", {"range": "269", "text": "270"}, "Update the dependencies array to be: [fetchDefectData, fetchUsers, user]", {"range": "271", "text": "272"}, "Update the dependencies array to be: [startDate, endDate, selectedUser, user.role, fetchDefectData]", {"range": "273", "text": "274"}, [31063, 31090], "[batchResults.length, roadClassificationEnabled]", [31326, 31340], "[cameraActive, handleLocationRequest, locationPermission]", [32547, 32574], "[cameraActive, coordinates, handleLocationRequest]", [29964, 29966], "[fetchData]", [3981, 3994], "[handleStopRecording, isRecording]", [22070, 22076], "[fetchDefectData, fetchUsers, user]", [22527, 22573], "[startDate, endDate, selectedUser, user.role, fetchDefectData]"]