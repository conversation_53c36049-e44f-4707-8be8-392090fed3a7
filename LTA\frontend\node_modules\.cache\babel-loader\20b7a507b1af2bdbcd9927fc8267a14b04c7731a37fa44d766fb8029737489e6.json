{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\LiveTracking.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { <PERSON><PERSON>, Badge, Card, Row, Col, Button } from 'react-bootstrap';\nimport { FaMapMarkerAlt, FaExclamationTriangle, FaRoute, FaClock, FaRoad, FaStop } from 'react-icons/fa';\nimport { watchPosition } from '../utils/deviceCoordinates';\nimport RouteMap from './RouteMap';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LiveTracking = ({\n  route,\n  onTrackingStart,\n  onTrackingStop,\n  onDeviationAlert,\n  isActive = false,\n  disabled = false\n}) => {\n  _s();\n  const [trackingSession, setTrackingSession] = useState(null);\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [trackingPoints, setTrackingPoints] = useState([]);\n  const [deviationAlerts, setDeviationAlerts] = useState([]);\n  const [stats, setStats] = useState({\n    total_distance: 0,\n    average_speed: 0,\n    duration_minutes: 0,\n    points_count: 0,\n    deviation_count: 0\n  });\n  const [error, setError] = useState('');\n  const [isTracking, setIsTracking] = useState(false);\n  const watchIdRef = useRef(null);\n  const lastUpdateRef = useRef(0);\n  const updateIntervalRef = useRef(null);\n\n  // Start tracking\n  const startTracking = useCallback(async () => {\n    if (!route || disabled) return;\n    try {\n      setError('');\n      console.log('🎯 Starting live tracking...');\n\n      // Start tracking session on backend\n      const response = await fetch('/api/location/tracking/start', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          planned_route: route.coordinates.map(coord => ({\n            latitude: coord[0],\n            longitude: coord[1]\n          })),\n          deviation_threshold: 50.0\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: Failed to start tracking`);\n      }\n      const data = await response.json();\n      if (!data.success) {\n        throw new Error(data.error || 'Failed to start tracking session');\n      }\n      const sessionId = data.session_id;\n      setTrackingSession({\n        sessionId,\n        deviationThreshold: data.deviation_threshold\n      });\n      setIsTracking(true);\n\n      // Start GPS watching\n      watchIdRef.current = watchPosition(handleLocationUpdate, {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 5000\n      });\n\n      // Start periodic stats updates\n      updateIntervalRef.current = setInterval(() => {\n        updateTrackingStats(sessionId);\n      }, 5000);\n      if (onTrackingStart) {\n        onTrackingStart(sessionId);\n      }\n      console.log('✅ Live tracking started with session:', sessionId);\n    } catch (error) {\n      console.error('❌ Failed to start tracking:', error);\n      setError(`Failed to start tracking: ${error.message}`);\n    }\n  }, [route, disabled, onTrackingStart]);\n\n  // Stop tracking\n  const stopTracking = useCallback(async () => {\n    if (!trackingSession) return;\n    try {\n      console.log('🛑 Stopping live tracking...');\n\n      // Stop GPS watching\n      if (watchIdRef.current) {\n        navigator.geolocation.clearWatch(watchIdRef.current);\n        watchIdRef.current = null;\n      }\n\n      // Stop stats updates\n      if (updateIntervalRef.current) {\n        clearInterval(updateIntervalRef.current);\n        updateIntervalRef.current = null;\n      }\n\n      // Stop tracking session on backend\n      const response = await fetch('/api/location/tracking/stop', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          session_id: trackingSession.sessionId\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          console.log('✅ Tracking session stopped:', data.session_data);\n        }\n      }\n      setIsTracking(false);\n      setTrackingSession(null);\n      if (onTrackingStop) {\n        onTrackingStop();\n      }\n    } catch (error) {\n      console.error('❌ Failed to stop tracking:', error);\n      setError(`Failed to stop tracking: ${error.message}`);\n    }\n  }, [trackingSession, onTrackingStop]);\n\n  // Handle location updates\n  const handleLocationUpdate = useCallback(async position => {\n    if (!trackingSession || position.error) {\n      if (position.error) {\n        console.warn('GPS error:', position.error);\n        setError(`GPS error: ${position.error}`);\n      }\n      return;\n    }\n    const now = Date.now();\n\n    // Throttle updates to avoid overwhelming the backend\n    if (now - lastUpdateRef.current < 2000) {\n      // 2 second minimum interval\n      return;\n    }\n    lastUpdateRef.current = now;\n    try {\n      const locationData = {\n        session_id: trackingSession.sessionId,\n        latitude: position.latitude,\n        longitude: position.longitude,\n        accuracy: position.accuracy,\n        speed: position.speed,\n        heading: position.heading,\n        altitude: position.altitude\n      };\n\n      // Update backend\n      const response = await fetch('/api/location/tracking/update', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(locationData)\n      });\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          // Update current location\n          setCurrentLocation({\n            latitude: position.latitude,\n            longitude: position.longitude,\n            accuracy: position.accuracy,\n            timestamp: new Date(position.timestamp)\n          });\n\n          // Add to tracking points\n          setTrackingPoints(prev => [...prev, {\n            latitude: position.latitude,\n            longitude: position.longitude,\n            timestamp: new Date(position.timestamp),\n            accuracy: position.accuracy\n          }]);\n\n          // Update stats\n          if (data.stats) {\n            setStats(data.stats);\n          }\n\n          // Handle deviation alert\n          if (data.deviation_alert) {\n            const alert = {\n              ...data.deviation_alert,\n              timestamp: new Date(data.deviation_alert.timestamp)\n            };\n            setDeviationAlerts(prev => [...prev, alert]);\n            if (onDeviationAlert) {\n              onDeviationAlert(alert);\n            }\n            console.warn('⚠️ Route deviation detected:', alert);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('❌ Failed to update location:', error);\n    }\n  }, [trackingSession, onDeviationAlert]);\n\n  // Update tracking stats\n  const updateTrackingStats = useCallback(async sessionId => {\n    try {\n      const response = await fetch(`/api/location/tracking/status/${sessionId}`);\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success && data.stats) {\n          setStats(data.stats);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to update stats:', error);\n    }\n  }, [setStats]);\n\n  // Auto-start/stop based on isActive prop\n  useEffect(() => {\n    if (isActive && !isTracking && route) {\n      startTracking();\n    } else if (!isActive && isTracking) {\n      stopTracking();\n    }\n  }, [isActive, isTracking, route, startTracking, stopTracking]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (watchIdRef.current) {\n        navigator.geolocation.clearWatch(watchIdRef.current);\n      }\n      if (updateIntervalRef.current) {\n        clearInterval(updateIntervalRef.current);\n      }\n    };\n  }, []);\n  const formatDuration = minutes => {\n    if (minutes < 60) {\n      return `${Math.round(minutes)} min`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.round(minutes % 60);\n      return `${hours}h ${mins}m`;\n    }\n  };\n  const formatDistance = meters => {\n    if (meters < 1000) {\n      return `${Math.round(meters)} m`;\n    } else {\n      return `${(meters / 1000).toFixed(1)} km`;\n    }\n  };\n  const formatSpeed = mps => {\n    if (!mps) return '0 km/h';\n    const kmh = mps * 3.6;\n    return `${kmh.toFixed(1)} km/h`;\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"live-tracking\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: `text-white ${isTracking ? 'bg-success' : 'bg-secondary'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center justify-content-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-0\",\n            children: \"Live Tracking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), isTracking && /*#__PURE__*/_jsxDEV(Badge, {\n            bg: \"light\",\n            text: \"dark\",\n            className: \"ms-2 pulse\",\n            children: \"ACTIVE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), !disabled && /*#__PURE__*/_jsxDEV(Button, {\n          variant: isTracking ? \"outline-light\" : \"light\",\n          size: \"sm\",\n          onClick: isTracking ? stopTracking : startTracking,\n          disabled: !route,\n          children: isTracking ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaStop, {\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 19\n            }, this), \"Stop\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 19\n            }, this), \"Start\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"warning\",\n        className: \"mb-3\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this), isTracking && /*#__PURE__*/_jsxDEV(Row, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-2 border rounded\",\n            children: [/*#__PURE__*/_jsxDEV(FaRoad, {\n              className: \"text-primary mb-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fw-bold\",\n              children: formatDistance(stats.total_distance)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"Distance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-2 border rounded\",\n            children: [/*#__PURE__*/_jsxDEV(FaClock, {\n              className: \"text-info mb-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fw-bold\",\n              children: formatDuration(stats.duration_minutes)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-2 border rounded\",\n            children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n              className: \"text-success mb-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fw-bold\",\n              children: formatSpeed(stats.average_speed)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"Avg Speed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-2 border rounded\",\n            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n              className: \"text-warning mb-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fw-bold\",\n              children: stats.deviation_count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: \"Deviations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), currentLocation && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"info\",\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Current Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small\",\n              children: [currentLocation.latitude.toFixed(6), \", \", currentLocation.longitude.toFixed(6), currentLocation.accuracy && ` • Accuracy: ±${currentLocation.accuracy.toFixed(0)}m`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this), deviationAlerts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-2\",\n          children: \"Recent Alerts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '150px',\n            overflowY: 'auto'\n          },\n          children: deviationAlerts.slice(-5).reverse().map((alert, index) => /*#__PURE__*/_jsxDEV(Alert, {\n            variant: alert.alert_type === 'critical' ? 'danger' : alert.alert_type === 'major' ? 'warning' : 'info',\n            className: \"py-2 mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-grow-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold\",\n                  children: alert.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: alert.timestamp.toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                bg: alert.alert_type === 'critical' ? 'danger' : alert.alert_type === 'major' ? 'warning' : 'info',\n                children: alert.alert_type.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 11\n      }, this), route && /*#__PURE__*/_jsxDEV(RouteMap, {\n        route: route,\n        pickup: route.pickup,\n        drop: route.drop,\n        currentLocation: currentLocation,\n        trackingPoints: trackingPoints,\n        height: \"300px\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 11\n      }, this), !isTracking && route && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"secondary\",\n        className: \"mt-3 mb-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), \"Click \\\"Start\\\" to begin live tracking during video recording. The system will monitor your location and alert you of route deviations.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .pulse {\n          animation: pulse 2s infinite;\n        }\n        \n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n};\n_s(LiveTracking, \"rP76kVwUcqr43BjN6N+3AugOFOc=\");\n_c = LiveTracking;\nexport default LiveTracking;\nvar _c;\n$RefreshReg$(_c, \"LiveTracking\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "<PERSON><PERSON>", "Badge", "Card", "Row", "Col", "<PERSON><PERSON>", "FaMapMarkerAlt", "FaExclamationTriangle", "FaRoute", "FaClock", "FaRoad", "FaStop", "watchPosition", "RouteMap", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LiveTracking", "route", "onTrackingStart", "onTrackingStop", "onDeviationAlert", "isActive", "disabled", "_s", "trackingSession", "setTrackingSession", "currentLocation", "setCurrentLocation", "trackingPoints", "setTrackingPoints", "deviationAlerts", "setDeviationAlerts", "stats", "setStats", "total_distance", "average_speed", "duration_minutes", "points_count", "deviation_count", "error", "setError", "isTracking", "setIsTracking", "watchIdRef", "lastUpdateRef", "updateIntervalRef", "startTracking", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "planned_route", "coordinates", "map", "coord", "latitude", "longitude", "deviation_threshold", "ok", "Error", "status", "data", "json", "success", "sessionId", "session_id", "deviationThreshold", "current", "handleLocationUpdate", "enableHighAccuracy", "timeout", "maximumAge", "setInterval", "updateTrackingStats", "message", "stopTracking", "navigator", "geolocation", "clearWatch", "clearInterval", "session_data", "position", "warn", "now", "Date", "locationData", "accuracy", "speed", "heading", "altitude", "timestamp", "prev", "deviation_alert", "alert", "formatDuration", "minutes", "Math", "round", "hours", "floor", "mins", "formatDistance", "meters", "toFixed", "formatSpeed", "mps", "kmh", "className", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bg", "text", "variant", "size", "onClick", "Body", "sm", "md", "length", "style", "maxHeight", "overflowY", "slice", "reverse", "index", "alert_type", "toLocaleTimeString", "toUpperCase", "pickup", "drop", "height", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/LiveTracking.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { <PERSON><PERSON>, Badge, Card, Row, Col, Button } from 'react-bootstrap';\nimport { FaMapMarkerAlt, FaExclamationTriangle, FaRoute, FaClock, FaRoad, FaStop } from 'react-icons/fa';\nimport { watchPosition } from '../utils/deviceCoordinates';\nimport RouteMap from './RouteMap';\n\nconst LiveTracking = ({ \n  route, \n  onTrackingStart, \n  onTrackingStop, \n  onDeviationAlert,\n  isActive = false,\n  disabled = false \n}) => {\n  const [trackingSession, setTrackingSession] = useState(null);\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [trackingPoints, setTrackingPoints] = useState([]);\n  const [deviationAlerts, setDeviationAlerts] = useState([]);\n  const [stats, setStats] = useState({\n    total_distance: 0,\n    average_speed: 0,\n    duration_minutes: 0,\n    points_count: 0,\n    deviation_count: 0\n  });\n  const [error, setError] = useState('');\n  const [isTracking, setIsTracking] = useState(false);\n  \n  const watchIdRef = useRef(null);\n  const lastUpdateRef = useRef(0);\n  const updateIntervalRef = useRef(null);\n\n  // Start tracking\n  const startTracking = useCallback(async () => {\n    if (!route || disabled) return;\n    \n    try {\n      setError('');\n      console.log('🎯 Starting live tracking...');\n      \n      // Start tracking session on backend\n      const response = await fetch('/api/location/tracking/start', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          planned_route: route.coordinates.map(coord => ({\n            latitude: coord[0],\n            longitude: coord[1]\n          })),\n          deviation_threshold: 50.0\n        })\n      });\n      \n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: Failed to start tracking`);\n      }\n      \n      const data = await response.json();\n      if (!data.success) {\n        throw new Error(data.error || 'Failed to start tracking session');\n      }\n      \n      const sessionId = data.session_id;\n      setTrackingSession({ sessionId, deviationThreshold: data.deviation_threshold });\n      setIsTracking(true);\n      \n      // Start GPS watching\n      watchIdRef.current = watchPosition(\n        handleLocationUpdate,\n        {\n          enableHighAccuracy: true,\n          timeout: 10000,\n          maximumAge: 5000\n        }\n      );\n      \n      // Start periodic stats updates\n      updateIntervalRef.current = setInterval(() => {\n        updateTrackingStats(sessionId);\n      }, 5000);\n      \n      if (onTrackingStart) {\n        onTrackingStart(sessionId);\n      }\n      \n      console.log('✅ Live tracking started with session:', sessionId);\n      \n    } catch (error) {\n      console.error('❌ Failed to start tracking:', error);\n      setError(`Failed to start tracking: ${error.message}`);\n    }\n  }, [route, disabled, onTrackingStart]);\n\n  // Stop tracking\n  const stopTracking = useCallback(async () => {\n    if (!trackingSession) return;\n    \n    try {\n      console.log('🛑 Stopping live tracking...');\n      \n      // Stop GPS watching\n      if (watchIdRef.current) {\n        navigator.geolocation.clearWatch(watchIdRef.current);\n        watchIdRef.current = null;\n      }\n      \n      // Stop stats updates\n      if (updateIntervalRef.current) {\n        clearInterval(updateIntervalRef.current);\n        updateIntervalRef.current = null;\n      }\n      \n      // Stop tracking session on backend\n      const response = await fetch('/api/location/tracking/stop', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          session_id: trackingSession.sessionId\n        })\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          console.log('✅ Tracking session stopped:', data.session_data);\n        }\n      }\n      \n      setIsTracking(false);\n      setTrackingSession(null);\n      \n      if (onTrackingStop) {\n        onTrackingStop();\n      }\n      \n    } catch (error) {\n      console.error('❌ Failed to stop tracking:', error);\n      setError(`Failed to stop tracking: ${error.message}`);\n    }\n  }, [trackingSession, onTrackingStop]);\n\n  // Handle location updates\n  const handleLocationUpdate = useCallback(async (position) => {\n    if (!trackingSession || position.error) {\n      if (position.error) {\n        console.warn('GPS error:', position.error);\n        setError(`GPS error: ${position.error}`);\n      }\n      return;\n    }\n    \n    const now = Date.now();\n    \n    // Throttle updates to avoid overwhelming the backend\n    if (now - lastUpdateRef.current < 2000) { // 2 second minimum interval\n      return;\n    }\n    \n    lastUpdateRef.current = now;\n    \n    try {\n      const locationData = {\n        session_id: trackingSession.sessionId,\n        latitude: position.latitude,\n        longitude: position.longitude,\n        accuracy: position.accuracy,\n        speed: position.speed,\n        heading: position.heading,\n        altitude: position.altitude\n      };\n      \n      // Update backend\n      const response = await fetch('/api/location/tracking/update', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(locationData)\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          // Update current location\n          setCurrentLocation({\n            latitude: position.latitude,\n            longitude: position.longitude,\n            accuracy: position.accuracy,\n            timestamp: new Date(position.timestamp)\n          });\n          \n          // Add to tracking points\n          setTrackingPoints(prev => [...prev, {\n            latitude: position.latitude,\n            longitude: position.longitude,\n            timestamp: new Date(position.timestamp),\n            accuracy: position.accuracy\n          }]);\n          \n          // Update stats\n          if (data.stats) {\n            setStats(data.stats);\n          }\n          \n          // Handle deviation alert\n          if (data.deviation_alert) {\n            const alert = {\n              ...data.deviation_alert,\n              timestamp: new Date(data.deviation_alert.timestamp)\n            };\n            \n            setDeviationAlerts(prev => [...prev, alert]);\n            \n            if (onDeviationAlert) {\n              onDeviationAlert(alert);\n            }\n            \n            console.warn('⚠️ Route deviation detected:', alert);\n          }\n        }\n      }\n      \n    } catch (error) {\n      console.error('❌ Failed to update location:', error);\n    }\n  }, [trackingSession, onDeviationAlert]);\n\n  // Update tracking stats\n  const updateTrackingStats = useCallback(async (sessionId) => {\n    try {\n      const response = await fetch(`/api/location/tracking/status/${sessionId}`);\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success && data.stats) {\n          setStats(data.stats);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to update stats:', error);\n    }\n  }, [setStats]);\n\n  // Auto-start/stop based on isActive prop\n  useEffect(() => {\n    if (isActive && !isTracking && route) {\n      startTracking();\n    } else if (!isActive && isTracking) {\n      stopTracking();\n    }\n  }, [isActive, isTracking, route, startTracking, stopTracking]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (watchIdRef.current) {\n        navigator.geolocation.clearWatch(watchIdRef.current);\n      }\n      if (updateIntervalRef.current) {\n        clearInterval(updateIntervalRef.current);\n      }\n    };\n  }, []);\n\n  const formatDuration = (minutes) => {\n    if (minutes < 60) {\n      return `${Math.round(minutes)} min`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.round(minutes % 60);\n      return `${hours}h ${mins}m`;\n    }\n  };\n\n  const formatDistance = (meters) => {\n    if (meters < 1000) {\n      return `${Math.round(meters)} m`;\n    } else {\n      return `${(meters / 1000).toFixed(1)} km`;\n    }\n  };\n\n  const formatSpeed = (mps) => {\n    if (!mps) return '0 km/h';\n    const kmh = mps * 3.6;\n    return `${kmh.toFixed(1)} km/h`;\n  };\n\n  return (\n    <Card className=\"live-tracking\">\n      <Card.Header className={`text-white ${isTracking ? 'bg-success' : 'bg-secondary'}`}>\n        <div className=\"d-flex align-items-center justify-content-between\">\n          <div className=\"d-flex align-items-center\">\n            <FaMapMarkerAlt className=\"me-2\" />\n            <h6 className=\"mb-0\">Live Tracking</h6>\n            {isTracking && (\n              <Badge bg=\"light\" text=\"dark\" className=\"ms-2 pulse\">\n                ACTIVE\n              </Badge>\n            )}\n          </div>\n          \n          {!disabled && (\n            <Button\n              variant={isTracking ? \"outline-light\" : \"light\"}\n              size=\"sm\"\n              onClick={isTracking ? stopTracking : startTracking}\n              disabled={!route}\n            >\n              {isTracking ? (\n                <>\n                  <FaStop className=\"me-1\" />\n                  Stop\n                </>\n              ) : (\n                <>\n                  <FaRoute className=\"me-1\" />\n                  Start\n                </>\n              )}\n            </Button>\n          )}\n        </div>\n      </Card.Header>\n      \n      <Card.Body>\n        {/* Error Display */}\n        {error && (\n          <Alert variant=\"warning\" className=\"mb-3\">\n            {error}\n          </Alert>\n        )}\n        \n        {/* Tracking Stats */}\n        {isTracking && (\n          <Row className=\"mb-3\">\n            <Col sm={6} md={3}>\n              <div className=\"text-center p-2 border rounded\">\n                <FaRoad className=\"text-primary mb-1\" />\n                <div className=\"fw-bold\">{formatDistance(stats.total_distance)}</div>\n                <small className=\"text-muted\">Distance</small>\n              </div>\n            </Col>\n            \n            <Col sm={6} md={3}>\n              <div className=\"text-center p-2 border rounded\">\n                <FaClock className=\"text-info mb-1\" />\n                <div className=\"fw-bold\">{formatDuration(stats.duration_minutes)}</div>\n                <small className=\"text-muted\">Duration</small>\n              </div>\n            </Col>\n            \n            <Col sm={6} md={3}>\n              <div className=\"text-center p-2 border rounded\">\n                <FaMapMarkerAlt className=\"text-success mb-1\" />\n                <div className=\"fw-bold\">{formatSpeed(stats.average_speed)}</div>\n                <small className=\"text-muted\">Avg Speed</small>\n              </div>\n            </Col>\n            \n            <Col sm={6} md={3}>\n              <div className=\"text-center p-2 border rounded\">\n                <FaExclamationTriangle className=\"text-warning mb-1\" />\n                <div className=\"fw-bold\">{stats.deviation_count}</div>\n                <small className=\"text-muted\">Deviations</small>\n              </div>\n            </Col>\n          </Row>\n        )}\n        \n        {/* Current Location */}\n        {currentLocation && (\n          <Alert variant=\"info\" className=\"mb-3\">\n            <div className=\"d-flex align-items-center\">\n              <FaMapMarkerAlt className=\"me-2\" />\n              <div>\n                <strong>Current Location</strong>\n                <div className=\"small\">\n                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}\n                  {currentLocation.accuracy && ` • Accuracy: ±${currentLocation.accuracy.toFixed(0)}m`}\n                </div>\n              </div>\n            </div>\n          </Alert>\n        )}\n        \n        {/* Recent Deviation Alerts */}\n        {deviationAlerts.length > 0 && (\n          <div className=\"mb-3\">\n            <h6 className=\"mb-2\">Recent Alerts</h6>\n            <div style={{ maxHeight: '150px', overflowY: 'auto' }}>\n              {deviationAlerts.slice(-5).reverse().map((alert, index) => (\n                <Alert \n                  key={index} \n                  variant={alert.alert_type === 'critical' ? 'danger' : alert.alert_type === 'major' ? 'warning' : 'info'}\n                  className=\"py-2 mb-2\"\n                >\n                  <div className=\"d-flex align-items-center\">\n                    <FaExclamationTriangle className=\"me-2\" />\n                    <div className=\"flex-grow-1\">\n                      <div className=\"fw-bold\">{alert.message}</div>\n                      <small className=\"text-muted\">\n                        {alert.timestamp.toLocaleTimeString()}\n                      </small>\n                    </div>\n                    <Badge bg={alert.alert_type === 'critical' ? 'danger' : alert.alert_type === 'major' ? 'warning' : 'info'}>\n                      {alert.alert_type.toUpperCase()}\n                    </Badge>\n                  </div>\n                </Alert>\n              ))}\n            </div>\n          </div>\n        )}\n        \n        {/* Live Map */}\n        {route && (\n          <RouteMap\n            route={route}\n            pickup={route.pickup}\n            drop={route.drop}\n            currentLocation={currentLocation}\n            trackingPoints={trackingPoints}\n            height=\"300px\"\n          />\n        )}\n        \n        {/* Instructions */}\n        {!isTracking && route && (\n          <Alert variant=\"secondary\" className=\"mt-3 mb-0\">\n            <div className=\"text-center\">\n              <FaRoute className=\"me-2\" />\n              Click \"Start\" to begin live tracking during video recording.\n              The system will monitor your location and alert you of route deviations.\n            </div>\n          </Alert>\n        )}\n      </Card.Body>\n      \n      <style jsx>{`\n        .pulse {\n          animation: pulse 2s infinite;\n        }\n        \n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n      `}</style>\n    </Card>\n  );\n};\n\nexport default LiveTracking;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AACtE,SAASC,cAAc,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACxG,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,YAAY,GAAGA,CAAC;EACpBC,KAAK;EACLC,eAAe;EACfC,cAAc;EACdC,gBAAgB;EAChBC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC;IACjCwC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMiD,UAAU,GAAG/C,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMgD,aAAa,GAAGhD,MAAM,CAAC,CAAC,CAAC;EAC/B,MAAMiD,iBAAiB,GAAGjD,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAMkD,aAAa,GAAGjD,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACoB,KAAK,IAAIK,QAAQ,EAAE;IAExB,IAAI;MACFkB,QAAQ,CAAC,EAAE,CAAC;MACZO,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;MAE3C;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,8BAA8B,EAAE;QAC3DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,aAAa,EAAEvC,KAAK,CAACwC,WAAW,CAACC,GAAG,CAACC,KAAK,KAAK;YAC7CC,QAAQ,EAAED,KAAK,CAAC,CAAC,CAAC;YAClBE,SAAS,EAAEF,KAAK,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC;UACHG,mBAAmB,EAAE;QACvB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACb,QAAQ,CAACc,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQf,QAAQ,CAACgB,MAAM,4BAA4B,CAAC;MACtE;MAEA,MAAMC,IAAI,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;MAClC,IAAI,CAACD,IAAI,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIJ,KAAK,CAACE,IAAI,CAAC3B,KAAK,IAAI,kCAAkC,CAAC;MACnE;MAEA,MAAM8B,SAAS,GAAGH,IAAI,CAACI,UAAU;MACjC7C,kBAAkB,CAAC;QAAE4C,SAAS;QAAEE,kBAAkB,EAAEL,IAAI,CAACJ;MAAoB,CAAC,CAAC;MAC/EpB,aAAa,CAAC,IAAI,CAAC;;MAEnB;MACAC,UAAU,CAAC6B,OAAO,GAAG9D,aAAa,CAChC+D,oBAAoB,EACpB;QACEC,kBAAkB,EAAE,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE;MACd,CACF,CAAC;;MAED;MACA/B,iBAAiB,CAAC2B,OAAO,GAAGK,WAAW,CAAC,MAAM;QAC5CC,mBAAmB,CAACT,SAAS,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC;MAER,IAAInD,eAAe,EAAE;QACnBA,eAAe,CAACmD,SAAS,CAAC;MAC5B;MAEAtB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEqB,SAAS,CAAC;IAEjE,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,6BAA6BD,KAAK,CAACwC,OAAO,EAAE,CAAC;IACxD;EACF,CAAC,EAAE,CAAC9D,KAAK,EAAEK,QAAQ,EAAEJ,eAAe,CAAC,CAAC;;EAEtC;EACA,MAAM8D,YAAY,GAAGnF,WAAW,CAAC,YAAY;IAC3C,IAAI,CAAC2B,eAAe,EAAE;IAEtB,IAAI;MACFuB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;MAE3C;MACA,IAAIL,UAAU,CAAC6B,OAAO,EAAE;QACtBS,SAAS,CAACC,WAAW,CAACC,UAAU,CAACxC,UAAU,CAAC6B,OAAO,CAAC;QACpD7B,UAAU,CAAC6B,OAAO,GAAG,IAAI;MAC3B;;MAEA;MACA,IAAI3B,iBAAiB,CAAC2B,OAAO,EAAE;QAC7BY,aAAa,CAACvC,iBAAiB,CAAC2B,OAAO,CAAC;QACxC3B,iBAAiB,CAAC2B,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,MAAMvB,QAAQ,GAAG,MAAMC,KAAK,CAAC,6BAA6B,EAAE;QAC1DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBe,UAAU,EAAE9C,eAAe,CAAC6C;QAC9B,CAAC;MACH,CAAC,CAAC;MAEF,IAAIpB,QAAQ,CAACc,EAAE,EAAE;QACf,MAAMG,IAAI,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChBrB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEkB,IAAI,CAACmB,YAAY,CAAC;QAC/D;MACF;MAEA3C,aAAa,CAAC,KAAK,CAAC;MACpBjB,kBAAkB,CAAC,IAAI,CAAC;MAExB,IAAIN,cAAc,EAAE;QAClBA,cAAc,CAAC,CAAC;MAClB;IAEF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,4BAA4BD,KAAK,CAACwC,OAAO,EAAE,CAAC;IACvD;EACF,CAAC,EAAE,CAACvD,eAAe,EAAEL,cAAc,CAAC,CAAC;;EAErC;EACA,MAAMsD,oBAAoB,GAAG5E,WAAW,CAAC,MAAOyF,QAAQ,IAAK;IAC3D,IAAI,CAAC9D,eAAe,IAAI8D,QAAQ,CAAC/C,KAAK,EAAE;MACtC,IAAI+C,QAAQ,CAAC/C,KAAK,EAAE;QAClBQ,OAAO,CAACwC,IAAI,CAAC,YAAY,EAAED,QAAQ,CAAC/C,KAAK,CAAC;QAC1CC,QAAQ,CAAC,cAAc8C,QAAQ,CAAC/C,KAAK,EAAE,CAAC;MAC1C;MACA;IACF;IAEA,MAAMiD,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIA,GAAG,GAAG5C,aAAa,CAAC4B,OAAO,GAAG,IAAI,EAAE;MAAE;MACxC;IACF;IAEA5B,aAAa,CAAC4B,OAAO,GAAGgB,GAAG;IAE3B,IAAI;MACF,MAAME,YAAY,GAAG;QACnBpB,UAAU,EAAE9C,eAAe,CAAC6C,SAAS;QACrCT,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;QAC3BC,SAAS,EAAEyB,QAAQ,CAACzB,SAAS;QAC7B8B,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;QAC3BC,KAAK,EAAEN,QAAQ,CAACM,KAAK;QACrBC,OAAO,EAAEP,QAAQ,CAACO,OAAO;QACzBC,QAAQ,EAAER,QAAQ,CAACQ;MACrB,CAAC;;MAED;MACA,MAAM7C,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+B,EAAE;QAC5DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACmC,YAAY;MACnC,CAAC,CAAC;MAEF,IAAIzC,QAAQ,CAACc,EAAE,EAAE;QACf,MAAMG,IAAI,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB;UACAzC,kBAAkB,CAAC;YACjBiC,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;YAC3BC,SAAS,EAAEyB,QAAQ,CAACzB,SAAS;YAC7B8B,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;YAC3BI,SAAS,EAAE,IAAIN,IAAI,CAACH,QAAQ,CAACS,SAAS;UACxC,CAAC,CAAC;;UAEF;UACAlE,iBAAiB,CAACmE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;YAClCpC,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;YAC3BC,SAAS,EAAEyB,QAAQ,CAACzB,SAAS;YAC7BkC,SAAS,EAAE,IAAIN,IAAI,CAACH,QAAQ,CAACS,SAAS,CAAC;YACvCJ,QAAQ,EAAEL,QAAQ,CAACK;UACrB,CAAC,CAAC,CAAC;;UAEH;UACA,IAAIzB,IAAI,CAAClC,KAAK,EAAE;YACdC,QAAQ,CAACiC,IAAI,CAAClC,KAAK,CAAC;UACtB;;UAEA;UACA,IAAIkC,IAAI,CAAC+B,eAAe,EAAE;YACxB,MAAMC,KAAK,GAAG;cACZ,GAAGhC,IAAI,CAAC+B,eAAe;cACvBF,SAAS,EAAE,IAAIN,IAAI,CAACvB,IAAI,CAAC+B,eAAe,CAACF,SAAS;YACpD,CAAC;YAEDhE,kBAAkB,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,KAAK,CAAC,CAAC;YAE5C,IAAI9E,gBAAgB,EAAE;cACpBA,gBAAgB,CAAC8E,KAAK,CAAC;YACzB;YAEAnD,OAAO,CAACwC,IAAI,CAAC,8BAA8B,EAAEW,KAAK,CAAC;UACrD;QACF;MACF;IAEF,CAAC,CAAC,OAAO3D,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAACf,eAAe,EAAEJ,gBAAgB,CAAC,CAAC;;EAEvC;EACA,MAAM0D,mBAAmB,GAAGjF,WAAW,CAAC,MAAOwE,SAAS,IAAK;IAC3D,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiCmB,SAAS,EAAE,CAAC;MAC1E,IAAIpB,QAAQ,CAACc,EAAE,EAAE;QACf,MAAMG,IAAI,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAAClC,KAAK,EAAE;UAC9BC,QAAQ,CAACiC,IAAI,CAAClC,KAAK,CAAC;QACtB;MACF;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;;EAEd;EACAtC,SAAS,CAAC,MAAM;IACd,IAAI0B,QAAQ,IAAI,CAACoB,UAAU,IAAIxB,KAAK,EAAE;MACpC6B,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM,IAAI,CAACzB,QAAQ,IAAIoB,UAAU,EAAE;MAClCuC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC3D,QAAQ,EAAEoB,UAAU,EAAExB,KAAK,EAAE6B,aAAa,EAAEkC,YAAY,CAAC,CAAC;;EAE9D;EACArF,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIgD,UAAU,CAAC6B,OAAO,EAAE;QACtBS,SAAS,CAACC,WAAW,CAACC,UAAU,CAACxC,UAAU,CAAC6B,OAAO,CAAC;MACtD;MACA,IAAI3B,iBAAiB,CAAC2B,OAAO,EAAE;QAC7BY,aAAa,CAACvC,iBAAiB,CAAC2B,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2B,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE;MAChB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC,MAAM;IACrC,CAAC,MAAM;MACL,MAAMG,KAAK,GAAGF,IAAI,CAACG,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMK,IAAI,GAAGJ,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;MACrC,OAAO,GAAGG,KAAK,KAAKE,IAAI,GAAG;IAC7B;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,IAAIA,MAAM,GAAG,IAAI,EAAE;MACjB,OAAO,GAAGN,IAAI,CAACC,KAAK,CAACK,MAAM,CAAC,IAAI;IAClC,CAAC,MAAM;MACL,OAAO,GAAG,CAACA,MAAM,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,KAAK;IAC3C;EACF,CAAC;EAED,MAAMC,WAAW,GAAIC,GAAG,IAAK;IAC3B,IAAI,CAACA,GAAG,EAAE,OAAO,QAAQ;IACzB,MAAMC,GAAG,GAAGD,GAAG,GAAG,GAAG;IACrB,OAAO,GAAGC,GAAG,CAACH,OAAO,CAAC,CAAC,CAAC,OAAO;EACjC,CAAC;EAED,oBACE/F,OAAA,CAACb,IAAI;IAACgH,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC7BpG,OAAA,CAACb,IAAI,CAACkH,MAAM;MAACF,SAAS,EAAE,cAAcvE,UAAU,GAAG,YAAY,GAAG,cAAc,EAAG;MAAAwE,QAAA,eACjFpG,OAAA;QAAKmG,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChEpG,OAAA;UAAKmG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCpG,OAAA,CAACT,cAAc;YAAC4G,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCzG,OAAA;YAAImG,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtC7E,UAAU,iBACT5B,OAAA,CAACd,KAAK;YAACwH,EAAE,EAAC,OAAO;YAACC,IAAI,EAAC,MAAM;YAACR,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAErD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL,CAAChG,QAAQ,iBACRT,OAAA,CAACV,MAAM;UACLsH,OAAO,EAAEhF,UAAU,GAAG,eAAe,GAAG,OAAQ;UAChDiF,IAAI,EAAC,IAAI;UACTC,OAAO,EAAElF,UAAU,GAAGuC,YAAY,GAAGlC,aAAc;UACnDxB,QAAQ,EAAE,CAACL,KAAM;UAAAgG,QAAA,EAEhBxE,UAAU,gBACT5B,OAAA,CAAAE,SAAA;YAAAkG,QAAA,gBACEpG,OAAA,CAACJ,MAAM;cAACuG,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAE7B;UAAA,eAAE,CAAC,gBAEHzG,OAAA,CAAAE,SAAA;YAAAkG,QAAA,gBACEpG,OAAA,CAACP,OAAO;cAAC0G,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAE9B;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdzG,OAAA,CAACb,IAAI,CAAC4H,IAAI;MAAAX,QAAA,GAEP1E,KAAK,iBACJ1B,OAAA,CAACf,KAAK;QAAC2H,OAAO,EAAC,SAAS;QAACT,SAAS,EAAC,MAAM;QAAAC,QAAA,EACtC1E;MAAK;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA7E,UAAU,iBACT5B,OAAA,CAACZ,GAAG;QAAC+G,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBpG,OAAA,CAACX,GAAG;UAAC2H,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eAChBpG,OAAA;YAAKmG,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CpG,OAAA,CAACL,MAAM;cAACwG,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxCzG,OAAA;cAAKmG,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEP,cAAc,CAAC1E,KAAK,CAACE,cAAc;YAAC;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrEzG,OAAA;cAAOmG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA,CAACX,GAAG;UAAC2H,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eAChBpG,OAAA;YAAKmG,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CpG,OAAA,CAACN,OAAO;cAACyG,SAAS,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtCzG,OAAA;cAAKmG,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEd,cAAc,CAACnE,KAAK,CAACI,gBAAgB;YAAC;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEzG,OAAA;cAAOmG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA,CAACX,GAAG;UAAC2H,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eAChBpG,OAAA;YAAKmG,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CpG,OAAA,CAACT,cAAc;cAAC4G,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDzG,OAAA;cAAKmG,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEJ,WAAW,CAAC7E,KAAK,CAACG,aAAa;YAAC;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjEzG,OAAA;cAAOmG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA,CAACX,GAAG;UAAC2H,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAb,QAAA,eAChBpG,OAAA;YAAKmG,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CpG,OAAA,CAACR,qBAAqB;cAAC2G,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDzG,OAAA;cAAKmG,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEjF,KAAK,CAACM;YAAe;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDzG,OAAA;cAAOmG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5F,eAAe,iBACdb,OAAA,CAACf,KAAK;QAAC2H,OAAO,EAAC,MAAM;QAACT,SAAS,EAAC,MAAM;QAAAC,QAAA,eACpCpG,OAAA;UAAKmG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCpG,OAAA,CAACT,cAAc;YAAC4G,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCzG,OAAA;YAAAoG,QAAA,gBACEpG,OAAA;cAAAoG,QAAA,EAAQ;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCzG,OAAA;cAAKmG,SAAS,EAAC,OAAO;cAAAC,QAAA,GACnBvF,eAAe,CAACkC,QAAQ,CAACgD,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAClF,eAAe,CAACmC,SAAS,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAC3ElF,eAAe,CAACiE,QAAQ,IAAI,iBAAiBjE,eAAe,CAACiE,QAAQ,CAACiB,OAAO,CAAC,CAAC,CAAC,GAAG;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGAxF,eAAe,CAACiG,MAAM,GAAG,CAAC,iBACzBlH,OAAA;QAAKmG,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBpG,OAAA;UAAImG,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCzG,OAAA;UAAKmH,KAAK,EAAE;YAAEC,SAAS,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAjB,QAAA,EACnDnF,eAAe,CAACqG,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC1E,GAAG,CAAC,CAACwC,KAAK,EAAEmC,KAAK,kBACpDxH,OAAA,CAACf,KAAK;YAEJ2H,OAAO,EAAEvB,KAAK,CAACoC,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAGpC,KAAK,CAACoC,UAAU,KAAK,OAAO,GAAG,SAAS,GAAG,MAAO;YACxGtB,SAAS,EAAC,WAAW;YAAAC,QAAA,eAErBpG,OAAA;cAAKmG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCpG,OAAA,CAACR,qBAAqB;gBAAC2G,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1CzG,OAAA;gBAAKmG,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpG,OAAA;kBAAKmG,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEf,KAAK,CAACnB;gBAAO;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CzG,OAAA;kBAAOmG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC1Bf,KAAK,CAACH,SAAS,CAACwC,kBAAkB,CAAC;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNzG,OAAA,CAACd,KAAK;gBAACwH,EAAE,EAAErB,KAAK,CAACoC,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAGpC,KAAK,CAACoC,UAAU,KAAK,OAAO,GAAG,SAAS,GAAG,MAAO;gBAAArB,QAAA,EACvGf,KAAK,CAACoC,UAAU,CAACE,WAAW,CAAC;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GAfDe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBL,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArG,KAAK,iBACJJ,OAAA,CAACF,QAAQ;QACPM,KAAK,EAAEA,KAAM;QACbwH,MAAM,EAAExH,KAAK,CAACwH,MAAO;QACrBC,IAAI,EAAEzH,KAAK,CAACyH,IAAK;QACjBhH,eAAe,EAAEA,eAAgB;QACjCE,cAAc,EAAEA,cAAe;QAC/B+G,MAAM,EAAC;MAAO;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACF,EAGA,CAAC7E,UAAU,IAAIxB,KAAK,iBACnBJ,OAAA,CAACf,KAAK;QAAC2H,OAAO,EAAC,WAAW;QAACT,SAAS,EAAC,WAAW;QAAAC,QAAA,eAC9CpG,OAAA;UAAKmG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpG,OAAA,CAACP,OAAO;YAAC0G,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2IAG9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEZzG,OAAA;MAAO+H,GAAG;MAAA3B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEX,CAAC;AAAC/F,EAAA,CAjcIP,YAAY;AAAA6H,EAAA,GAAZ7H,YAAY;AAmclB,eAAeA,YAAY;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}