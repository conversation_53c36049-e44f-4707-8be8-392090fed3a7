{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\RouteMap.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport 'leaflet/dist/leaflet.css';\n\n// Fix for default markers in react-leaflet\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),\n  iconUrl: require('leaflet/dist/images/marker-icon.png'),\n  shadowUrl: require('leaflet/dist/images/marker-shadow.png')\n});\n\n// Custom icons\nconst pickupIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#28a745\" width=\"24\" height=\"24\">\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>\n  `),\n  iconSize: [32, 32],\n  iconAnchor: [16, 32],\n  popupAnchor: [0, -32]\n});\nconst dropIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#dc3545\" width=\"24\" height=\"24\">\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>\n  `),\n  iconSize: [32, 32],\n  iconAnchor: [16, 32],\n  popupAnchor: [0, -32]\n});\nconst currentLocationIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#007bff\" width=\"24\" height=\"24\">\n      <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#007bff\" stroke=\"#ffffff\" stroke-width=\"2\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#ffffff\"/>\n    </svg>\n  `),\n  iconSize: [24, 24],\n  iconAnchor: [12, 12],\n  popupAnchor: [0, -12]\n});\n\n// Component to fit map bounds to route\nconst FitBounds = ({\n  bounds\n}) => {\n  _s();\n  const map = useMap();\n  useEffect(() => {\n    if (bounds && bounds.length > 0) {\n      const leafletBounds = L.latLngBounds(bounds);\n      map.fitBounds(leafletBounds, {\n        padding: [20, 20]\n      });\n    }\n  }, [map, bounds]);\n  return null;\n};\n_s(FitBounds, \"IoceErwr5KVGS9kN4RQ1bOkYMAg=\", false, function () {\n  return [useMap];\n});\n_c = FitBounds;\nconst RouteMap = ({\n  route,\n  pickup,\n  drop,\n  currentLocation = null,\n  trackingPoints = [],\n  height = '400px',\n  showInstructions = false\n}) => {\n  _s2();\n  const mapRef = useRef();\n\n  // Calculate bounds for the route\n  const getBounds = () => {\n    const bounds = [];\n    if (pickup) {\n      bounds.push([pickup.latitude, pickup.longitude]);\n    }\n    if (drop) {\n      bounds.push([drop.latitude, drop.longitude]);\n    }\n    if (route && route.coordinates) {\n      route.coordinates.forEach(coord => {\n        bounds.push([coord[0], coord[1]]);\n      });\n    }\n    if (currentLocation) {\n      bounds.push([currentLocation.latitude, currentLocation.longitude]);\n    }\n    if (trackingPoints && trackingPoints.length > 0) {\n      trackingPoints.forEach(point => {\n        bounds.push([point.latitude, point.longitude]);\n      });\n    }\n    return bounds;\n  };\n\n  // Get center point for map\n  const getCenter = () => {\n    if (route && route.coordinates && route.coordinates.length > 0) {\n      const midIndex = Math.floor(route.coordinates.length / 2);\n      return [route.coordinates[midIndex][0], route.coordinates[midIndex][1]];\n    }\n    if (pickup) {\n      return [pickup.latitude, pickup.longitude];\n    }\n\n    // Default to Bangalore\n    return [12.9716, 77.5946];\n  };\n  const bounds = getBounds();\n  const center = getCenter();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"route-map\",\n    style: {\n      height,\n      width: '100%',\n      borderRadius: '8px',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n      ref: mapRef,\n      center: center,\n      zoom: 13,\n      style: {\n        height: '100%',\n        width: '100%'\n      },\n      scrollWheelZoom: true,\n      children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n        attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",\n        url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FitBounds, {\n        bounds: bounds\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), pickup && /*#__PURE__*/_jsxDEV(Marker, {\n        position: [pickup.latitude, pickup.longitude],\n        icon: pickupIcon,\n        children: /*#__PURE__*/_jsxDEV(Popup, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Pickup Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 49\n            }, this), pickup.address, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [pickup.latitude.toFixed(6), \", \", pickup.longitude.toFixed(6)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), drop && /*#__PURE__*/_jsxDEV(Marker, {\n        position: [drop.latitude, drop.longitude],\n        icon: dropIcon,\n        children: /*#__PURE__*/_jsxDEV(Popup, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Drop Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 47\n            }, this), drop.address, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 31\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [drop.latitude.toFixed(6), \", \", drop.longitude.toFixed(6)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this), currentLocation && /*#__PURE__*/_jsxDEV(Marker, {\n        position: [currentLocation.latitude, currentLocation.longitude],\n        icon: currentLocationIcon,\n        children: /*#__PURE__*/_jsxDEV(Popup, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Current Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 50\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [currentLocation.latitude.toFixed(6), \", \", currentLocation.longitude.toFixed(6), currentLocation.accuracy && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this), \"Accuracy: \\xB1\", currentLocation.accuracy.toFixed(0), \"m\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), route && route.coordinates && route.coordinates.length > 0 && /*#__PURE__*/_jsxDEV(Polyline, {\n        positions: route.coordinates,\n        color: \"#007bff\",\n        weight: 4,\n        opacity: 0.8\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), trackingPoints && trackingPoints.length > 1 && /*#__PURE__*/_jsxDEV(Polyline, {\n        positions: trackingPoints.map(point => [point.latitude, point.longitude]),\n        color: \"#28a745\",\n        weight: 3,\n        opacity: 0.9,\n        dashArray: \"5, 5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), route && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"route-info-overlay position-absolute top-0 start-0 m-2 p-2 bg-white rounded shadow-sm\",\n      style: {\n        zIndex: 1000,\n        fontSize: '0.875rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge bg-primary me-2\",\n          children: \"Route\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [route.distance_km.toFixed(1), \" km\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted\",\n        children: [\"Duration: \", Math.round(route.duration_minutes), \" min\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), route.profile && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-muted\",\n        children: [\"Mode: \", route.profile]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"map-legend position-absolute bottom-0 start-0 m-2 p-2 bg-white rounded shadow-sm\",\n      style: {\n        zIndex: 1000,\n        fontSize: '0.75rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex flex-column gap-1\",\n        children: [pickup && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"me-2\",\n            style: {\n              width: '12px',\n              height: '12px',\n              backgroundColor: '#28a745',\n              borderRadius: '50%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Pickup\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), drop && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"me-2\",\n            style: {\n              width: '12px',\n              height: '12px',\n              backgroundColor: '#dc3545',\n              borderRadius: '50%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Drop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), route && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"me-2\",\n            style: {\n              width: '12px',\n              height: '2px',\n              backgroundColor: '#007bff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Planned Route\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), trackingPoints && trackingPoints.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"me-2\",\n            style: {\n              width: '12px',\n              height: '2px',\n              backgroundColor: '#28a745',\n              borderTop: '2px dashed #28a745'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Actual Path\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), currentLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"me-2\",\n            style: {\n              width: '12px',\n              height: '12px',\n              backgroundColor: '#007bff',\n              borderRadius: '50%',\n              border: '2px solid white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Current\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s2(RouteMap, \"eZwvXZNGrOinO8i65lLhOza0GRY=\");\n_c2 = RouteMap;\nexport default RouteMap;\nvar _c, _c2;\n$RefreshReg$(_c, \"FitBounds\");\n$RefreshReg$(_c2, \"RouteMap\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "Polyline", "useMap", "L", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "require", "iconUrl", "shadowUrl", "pickupIcon", "btoa", "iconSize", "iconAnchor", "popupAnchor", "dropIcon", "currentLocationIcon", "FitBounds", "bounds", "_s", "map", "length", "leafletBounds", "latLngBounds", "fitBounds", "padding", "_c", "RouteMap", "route", "pickup", "drop", "currentLocation", "trackingPoints", "height", "showInstructions", "_s2", "mapRef", "getBounds", "push", "latitude", "longitude", "coordinates", "for<PERSON>ach", "coord", "point", "getCenter", "midIndex", "Math", "floor", "center", "className", "style", "width", "borderRadius", "overflow", "children", "ref", "zoom", "scrollWheelZoom", "attribution", "url", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "icon", "address", "toFixed", "accuracy", "positions", "color", "weight", "opacity", "dashArray", "zIndex", "fontSize", "distance_km", "round", "duration_minutes", "profile", "backgroundColor", "borderTop", "border", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/RouteMap.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';\nimport L from 'leaflet';\nimport 'leaflet/dist/leaflet.css';\n\n// Fix for default markers in react-leaflet\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),\n  iconUrl: require('leaflet/dist/images/marker-icon.png'),\n  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),\n});\n\n// Custom icons\nconst pickupIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#28a745\" width=\"24\" height=\"24\">\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>\n  `),\n  iconSize: [32, 32],\n  iconAnchor: [16, 32],\n  popupAnchor: [0, -32],\n});\n\nconst dropIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#dc3545\" width=\"24\" height=\"24\">\n      <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n    </svg>\n  `),\n  iconSize: [32, 32],\n  iconAnchor: [16, 32],\n  popupAnchor: [0, -32],\n});\n\nconst currentLocationIcon = new L.Icon({\n  iconUrl: 'data:image/svg+xml;base64,' + btoa(`\n    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#007bff\" width=\"24\" height=\"24\">\n      <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#007bff\" stroke=\"#ffffff\" stroke-width=\"2\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#ffffff\"/>\n    </svg>\n  `),\n  iconSize: [24, 24],\n  iconAnchor: [12, 12],\n  popupAnchor: [0, -12],\n});\n\n// Component to fit map bounds to route\nconst FitBounds = ({ bounds }) => {\n  const map = useMap();\n  \n  useEffect(() => {\n    if (bounds && bounds.length > 0) {\n      const leafletBounds = L.latLngBounds(bounds);\n      map.fitBounds(leafletBounds, { padding: [20, 20] });\n    }\n  }, [map, bounds]);\n  \n  return null;\n};\n\nconst RouteMap = ({ \n  route, \n  pickup, \n  drop, \n  currentLocation = null,\n  trackingPoints = [],\n  height = '400px',\n  showInstructions = false \n}) => {\n  const mapRef = useRef();\n\n  // Calculate bounds for the route\n  const getBounds = () => {\n    const bounds = [];\n    \n    if (pickup) {\n      bounds.push([pickup.latitude, pickup.longitude]);\n    }\n    \n    if (drop) {\n      bounds.push([drop.latitude, drop.longitude]);\n    }\n    \n    if (route && route.coordinates) {\n      route.coordinates.forEach(coord => {\n        bounds.push([coord[0], coord[1]]);\n      });\n    }\n    \n    if (currentLocation) {\n      bounds.push([currentLocation.latitude, currentLocation.longitude]);\n    }\n    \n    if (trackingPoints && trackingPoints.length > 0) {\n      trackingPoints.forEach(point => {\n        bounds.push([point.latitude, point.longitude]);\n      });\n    }\n    \n    return bounds;\n  };\n\n  // Get center point for map\n  const getCenter = () => {\n    if (route && route.coordinates && route.coordinates.length > 0) {\n      const midIndex = Math.floor(route.coordinates.length / 2);\n      return [route.coordinates[midIndex][0], route.coordinates[midIndex][1]];\n    }\n    \n    if (pickup) {\n      return [pickup.latitude, pickup.longitude];\n    }\n    \n    // Default to Bangalore\n    return [12.9716, 77.5946];\n  };\n\n  const bounds = getBounds();\n  const center = getCenter();\n\n  return (\n    <div className=\"route-map\" style={{ height, width: '100%', borderRadius: '8px', overflow: 'hidden' }}>\n      <MapContainer\n        ref={mapRef}\n        center={center}\n        zoom={13}\n        style={{ height: '100%', width: '100%' }}\n        scrollWheelZoom={true}\n      >\n        <TileLayer\n          attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n          url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n        />\n        \n        {/* Fit bounds to show entire route */}\n        <FitBounds bounds={bounds} />\n        \n        {/* Pickup marker */}\n        {pickup && (\n          <Marker \n            position={[pickup.latitude, pickup.longitude]} \n            icon={pickupIcon}\n          >\n            <Popup>\n              <div>\n                <strong>Pickup Location</strong><br />\n                {pickup.address}<br />\n                <small className=\"text-muted\">\n                  {pickup.latitude.toFixed(6)}, {pickup.longitude.toFixed(6)}\n                </small>\n              </div>\n            </Popup>\n          </Marker>\n        )}\n        \n        {/* Drop marker */}\n        {drop && (\n          <Marker \n            position={[drop.latitude, drop.longitude]} \n            icon={dropIcon}\n          >\n            <Popup>\n              <div>\n                <strong>Drop Location</strong><br />\n                {drop.address}<br />\n                <small className=\"text-muted\">\n                  {drop.latitude.toFixed(6)}, {drop.longitude.toFixed(6)}\n                </small>\n              </div>\n            </Popup>\n          </Marker>\n        )}\n        \n        {/* Current location marker */}\n        {currentLocation && (\n          <Marker \n            position={[currentLocation.latitude, currentLocation.longitude]} \n            icon={currentLocationIcon}\n          >\n            <Popup>\n              <div>\n                <strong>Current Location</strong><br />\n                <small className=\"text-muted\">\n                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}\n                  {currentLocation.accuracy && (\n                    <><br />Accuracy: ±{currentLocation.accuracy.toFixed(0)}m</>\n                  )}\n                </small>\n              </div>\n            </Popup>\n          </Marker>\n        )}\n        \n        {/* Route polyline */}\n        {route && route.coordinates && route.coordinates.length > 0 && (\n          <Polyline\n            positions={route.coordinates}\n            color=\"#007bff\"\n            weight={4}\n            opacity={0.8}\n          />\n        )}\n        \n        {/* Tracking points polyline (actual path taken) */}\n        {trackingPoints && trackingPoints.length > 1 && (\n          <Polyline\n            positions={trackingPoints.map(point => [point.latitude, point.longitude])}\n            color=\"#28a745\"\n            weight={3}\n            opacity={0.9}\n            dashArray=\"5, 5\"\n          />\n        )}\n      </MapContainer>\n      \n      {/* Route information overlay */}\n      {route && (\n        <div \n          className=\"route-info-overlay position-absolute top-0 start-0 m-2 p-2 bg-white rounded shadow-sm\"\n          style={{ zIndex: 1000, fontSize: '0.875rem' }}\n        >\n          <div className=\"d-flex align-items-center mb-1\">\n            <span className=\"badge bg-primary me-2\">Route</span>\n            <strong>{route.distance_km.toFixed(1)} km</strong>\n          </div>\n          <div className=\"text-muted\">\n            Duration: {Math.round(route.duration_minutes)} min\n          </div>\n          {route.profile && (\n            <div className=\"text-muted\">\n              Mode: {route.profile}\n            </div>\n          )}\n        </div>\n      )}\n      \n      {/* Legend */}\n      <div \n        className=\"map-legend position-absolute bottom-0 start-0 m-2 p-2 bg-white rounded shadow-sm\"\n        style={{ zIndex: 1000, fontSize: '0.75rem' }}\n      >\n        <div className=\"d-flex flex-column gap-1\">\n          {pickup && (\n            <div className=\"d-flex align-items-center\">\n              <div className=\"me-2\" style={{ width: '12px', height: '12px', backgroundColor: '#28a745', borderRadius: '50%' }}></div>\n              <span>Pickup</span>\n            </div>\n          )}\n          {drop && (\n            <div className=\"d-flex align-items-center\">\n              <div className=\"me-2\" style={{ width: '12px', height: '12px', backgroundColor: '#dc3545', borderRadius: '50%' }}></div>\n              <span>Drop</span>\n            </div>\n          )}\n          {route && (\n            <div className=\"d-flex align-items-center\">\n              <div className=\"me-2\" style={{ width: '12px', height: '2px', backgroundColor: '#007bff' }}></div>\n              <span>Planned Route</span>\n            </div>\n          )}\n          {trackingPoints && trackingPoints.length > 0 && (\n            <div className=\"d-flex align-items-center\">\n              <div className=\"me-2\" style={{ width: '12px', height: '2px', backgroundColor: '#28a745', borderTop: '2px dashed #28a745' }}></div>\n              <span>Actual Path</span>\n            </div>\n          )}\n          {currentLocation && (\n            <div className=\"d-flex align-items-center\">\n              <div className=\"me-2\" style={{ width: '12px', height: '12px', backgroundColor: '#007bff', borderRadius: '50%', border: '2px solid white' }}></div>\n              <span>Current</span>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RouteMap;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AACxF,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAO,0BAA0B;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,OAAOJ,CAAC,CAACK,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CR,CAAC,CAACK,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EAAEC,OAAO,CAAC,wCAAwC,CAAC;EAChEC,OAAO,EAAED,OAAO,CAAC,qCAAqC,CAAC;EACvDE,SAAS,EAAEF,OAAO,CAAC,uCAAuC;AAC5D,CAAC,CAAC;;AAEF;AACA,MAAMG,UAAU,GAAG,IAAId,CAAC,CAACK,IAAI,CAAC;EAC5BO,OAAO,EAAE,4BAA4B,GAAGG,IAAI,CAAC;AAC/C;AACA;AACA;AACA,GAAG,CAAC;EACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,CAAC,CAAC;AAEF,MAAMC,QAAQ,GAAG,IAAInB,CAAC,CAACK,IAAI,CAAC;EAC1BO,OAAO,EAAE,4BAA4B,GAAGG,IAAI,CAAC;AAC/C;AACA;AACA;AACA,GAAG,CAAC;EACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,CAAC,CAAC;AAEF,MAAME,mBAAmB,GAAG,IAAIpB,CAAC,CAACK,IAAI,CAAC;EACrCO,OAAO,EAAE,4BAA4B,GAAGG,IAAI,CAAC;AAC/C;AACA;AACA;AACA;AACA,GAAG,CAAC;EACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AACtB,CAAC,CAAC;;AAEF;AACA,MAAMG,SAAS,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAMC,GAAG,GAAGzB,MAAM,CAAC,CAAC;EAEpBP,SAAS,CAAC,MAAM;IACd,IAAI8B,MAAM,IAAIA,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAMC,aAAa,GAAG1B,CAAC,CAAC2B,YAAY,CAACL,MAAM,CAAC;MAC5CE,GAAG,CAACI,SAAS,CAACF,aAAa,EAAE;QAAEG,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE;MAAE,CAAC,CAAC;IACrD;EACF,CAAC,EAAE,CAACL,GAAG,EAAEF,MAAM,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb,CAAC;AAACC,EAAA,CAXIF,SAAS;EAAA,QACDtB,MAAM;AAAA;AAAA+B,EAAA,GADdT,SAAS;AAaf,MAAMU,QAAQ,GAAGA,CAAC;EAChBC,KAAK;EACLC,MAAM;EACNC,IAAI;EACJC,eAAe,GAAG,IAAI;EACtBC,cAAc,GAAG,EAAE;EACnBC,MAAM,GAAG,OAAO;EAChBC,gBAAgB,GAAG;AACrB,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAMC,MAAM,GAAG/C,MAAM,CAAC,CAAC;;EAEvB;EACA,MAAMgD,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMnB,MAAM,GAAG,EAAE;IAEjB,IAAIW,MAAM,EAAE;MACVX,MAAM,CAACoB,IAAI,CAAC,CAACT,MAAM,CAACU,QAAQ,EAAEV,MAAM,CAACW,SAAS,CAAC,CAAC;IAClD;IAEA,IAAIV,IAAI,EAAE;MACRZ,MAAM,CAACoB,IAAI,CAAC,CAACR,IAAI,CAACS,QAAQ,EAAET,IAAI,CAACU,SAAS,CAAC,CAAC;IAC9C;IAEA,IAAIZ,KAAK,IAAIA,KAAK,CAACa,WAAW,EAAE;MAC9Bb,KAAK,CAACa,WAAW,CAACC,OAAO,CAACC,KAAK,IAAI;QACjCzB,MAAM,CAACoB,IAAI,CAAC,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC;IACJ;IAEA,IAAIZ,eAAe,EAAE;MACnBb,MAAM,CAACoB,IAAI,CAAC,CAACP,eAAe,CAACQ,QAAQ,EAAER,eAAe,CAACS,SAAS,CAAC,CAAC;IACpE;IAEA,IAAIR,cAAc,IAAIA,cAAc,CAACX,MAAM,GAAG,CAAC,EAAE;MAC/CW,cAAc,CAACU,OAAO,CAACE,KAAK,IAAI;QAC9B1B,MAAM,CAACoB,IAAI,CAAC,CAACM,KAAK,CAACL,QAAQ,EAAEK,KAAK,CAACJ,SAAS,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ;IAEA,OAAOtB,MAAM;EACf,CAAC;;EAED;EACA,MAAM2B,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIjB,KAAK,IAAIA,KAAK,CAACa,WAAW,IAAIb,KAAK,CAACa,WAAW,CAACpB,MAAM,GAAG,CAAC,EAAE;MAC9D,MAAMyB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACpB,KAAK,CAACa,WAAW,CAACpB,MAAM,GAAG,CAAC,CAAC;MACzD,OAAO,CAACO,KAAK,CAACa,WAAW,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAElB,KAAK,CAACa,WAAW,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE;IAEA,IAAIjB,MAAM,EAAE;MACV,OAAO,CAACA,MAAM,CAACU,QAAQ,EAAEV,MAAM,CAACW,SAAS,CAAC;IAC5C;;IAEA;IACA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;EAC3B,CAAC;EAED,MAAMtB,MAAM,GAAGmB,SAAS,CAAC,CAAC;EAC1B,MAAMY,MAAM,GAAGJ,SAAS,CAAC,CAAC;EAE1B,oBACE/C,OAAA;IAAKoD,SAAS,EAAC,WAAW;IAACC,KAAK,EAAE;MAAElB,MAAM;MAAEmB,KAAK,EAAE,MAAM;MAAEC,YAAY,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACnGzD,OAAA,CAACR,YAAY;MACXkE,GAAG,EAAEpB,MAAO;MACZa,MAAM,EAAEA,MAAO;MACfQ,IAAI,EAAE,EAAG;MACTN,KAAK,EAAE;QAAElB,MAAM,EAAE,MAAM;QAAEmB,KAAK,EAAE;MAAO,CAAE;MACzCM,eAAe,EAAE,IAAK;MAAAH,QAAA,gBAEtBzD,OAAA,CAACP,SAAS;QACRoE,WAAW,EAAC,yFAAyF;QACrGC,GAAG,EAAC;MAAoD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eAGFlE,OAAA,CAACmB,SAAS;QAACC,MAAM,EAAEA;MAAO;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAG5BnC,MAAM,iBACL/B,OAAA,CAACN,MAAM;QACLyE,QAAQ,EAAE,CAACpC,MAAM,CAACU,QAAQ,EAAEV,MAAM,CAACW,SAAS,CAAE;QAC9C0B,IAAI,EAAExD,UAAW;QAAA6C,QAAA,eAEjBzD,OAAA,CAACL,KAAK;UAAA8D,QAAA,eACJzD,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAAyD,QAAA,EAAQ;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAlE,OAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACrCnC,MAAM,CAACsC,OAAO,eAACrE,OAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtBlE,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAK,QAAA,GAC1B1B,MAAM,CAACU,QAAQ,CAAC6B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACvC,MAAM,CAACW,SAAS,CAAC4B,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT,EAGAlC,IAAI,iBACHhC,OAAA,CAACN,MAAM;QACLyE,QAAQ,EAAE,CAACnC,IAAI,CAACS,QAAQ,EAAET,IAAI,CAACU,SAAS,CAAE;QAC1C0B,IAAI,EAAEnD,QAAS;QAAAwC,QAAA,eAEfzD,OAAA,CAACL,KAAK;UAAA8D,QAAA,eACJzD,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAAyD,QAAA,EAAQ;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAlE,OAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACnClC,IAAI,CAACqC,OAAO,eAACrE,OAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpBlE,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAK,QAAA,GAC1BzB,IAAI,CAACS,QAAQ,CAAC6B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACtC,IAAI,CAACU,SAAS,CAAC4B,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT,EAGAjC,eAAe,iBACdjC,OAAA,CAACN,MAAM;QACLyE,QAAQ,EAAE,CAAClC,eAAe,CAACQ,QAAQ,EAAER,eAAe,CAACS,SAAS,CAAE;QAChE0B,IAAI,EAAElD,mBAAoB;QAAAuC,QAAA,eAE1BzD,OAAA,CAACL,KAAK;UAAA8D,QAAA,eACJzD,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAAyD,QAAA,EAAQ;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAlE,OAAA;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvClE,OAAA;cAAOoD,SAAS,EAAC,YAAY;cAAAK,QAAA,GAC1BxB,eAAe,CAACQ,QAAQ,CAAC6B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACrC,eAAe,CAACS,SAAS,CAAC4B,OAAO,CAAC,CAAC,CAAC,EAC3ErC,eAAe,CAACsC,QAAQ,iBACvBvE,OAAA,CAAAE,SAAA;gBAAAuD,QAAA,gBAAEzD,OAAA;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,kBAAW,EAACjC,eAAe,CAACsC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA,eAAE,CAC5D;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACT,EAGApC,KAAK,IAAIA,KAAK,CAACa,WAAW,IAAIb,KAAK,CAACa,WAAW,CAACpB,MAAM,GAAG,CAAC,iBACzDvB,OAAA,CAACJ,QAAQ;QACP4E,SAAS,EAAE1C,KAAK,CAACa,WAAY;QAC7B8B,KAAK,EAAC,SAAS;QACfC,MAAM,EAAE,CAAE;QACVC,OAAO,EAAE;MAAI;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CACF,EAGAhC,cAAc,IAAIA,cAAc,CAACX,MAAM,GAAG,CAAC,iBAC1CvB,OAAA,CAACJ,QAAQ;QACP4E,SAAS,EAAEtC,cAAc,CAACZ,GAAG,CAACwB,KAAK,IAAI,CAACA,KAAK,CAACL,QAAQ,EAAEK,KAAK,CAACJ,SAAS,CAAC,CAAE;QAC1E+B,KAAK,EAAC,SAAS;QACfC,MAAM,EAAE,CAAE;QACVC,OAAO,EAAE,GAAI;QACbC,SAAS,EAAC;MAAM;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAGdpC,KAAK,iBACJ9B,OAAA;MACEoD,SAAS,EAAC,uFAAuF;MACjGC,KAAK,EAAE;QAAEwB,MAAM,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAW,CAAE;MAAArB,QAAA,gBAE9CzD,OAAA;QAAKoD,SAAS,EAAC,gCAAgC;QAAAK,QAAA,gBAC7CzD,OAAA;UAAMoD,SAAS,EAAC,uBAAuB;UAAAK,QAAA,EAAC;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpDlE,OAAA;UAAAyD,QAAA,GAAS3B,KAAK,CAACiD,WAAW,CAACT,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNlE,OAAA;QAAKoD,SAAS,EAAC,YAAY;QAAAK,QAAA,GAAC,YAChB,EAACR,IAAI,CAAC+B,KAAK,CAAClD,KAAK,CAACmD,gBAAgB,CAAC,EAAC,MAChD;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACLpC,KAAK,CAACoD,OAAO,iBACZlF,OAAA;QAAKoD,SAAS,EAAC,YAAY;QAAAK,QAAA,GAAC,QACpB,EAAC3B,KAAK,CAACoD,OAAO;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDlE,OAAA;MACEoD,SAAS,EAAC,kFAAkF;MAC5FC,KAAK,EAAE;QAAEwB,MAAM,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAU,CAAE;MAAArB,QAAA,eAE7CzD,OAAA;QAAKoD,SAAS,EAAC,0BAA0B;QAAAK,QAAA,GACtC1B,MAAM,iBACL/B,OAAA;UAAKoD,SAAS,EAAC,2BAA2B;UAAAK,QAAA,gBACxCzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEnB,MAAM,EAAE,MAAM;cAAEgD,eAAe,EAAE,SAAS;cAAE5B,YAAY,EAAE;YAAM;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvHlE,OAAA;YAAAyD,QAAA,EAAM;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACN,EACAlC,IAAI,iBACHhC,OAAA;UAAKoD,SAAS,EAAC,2BAA2B;UAAAK,QAAA,gBACxCzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEnB,MAAM,EAAE,MAAM;cAAEgD,eAAe,EAAE,SAAS;cAAE5B,YAAY,EAAE;YAAM;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvHlE,OAAA;YAAAyD,QAAA,EAAM;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN,EACApC,KAAK,iBACJ9B,OAAA;UAAKoD,SAAS,EAAC,2BAA2B;UAAAK,QAAA,gBACxCzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEnB,MAAM,EAAE,KAAK;cAAEgD,eAAe,EAAE;YAAU;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjGlE,OAAA;YAAAyD,QAAA,EAAM;UAAa;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACN,EACAhC,cAAc,IAAIA,cAAc,CAACX,MAAM,GAAG,CAAC,iBAC1CvB,OAAA;UAAKoD,SAAS,EAAC,2BAA2B;UAAAK,QAAA,gBACxCzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEnB,MAAM,EAAE,KAAK;cAAEgD,eAAe,EAAE,SAAS;cAAEC,SAAS,EAAE;YAAqB;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClIlE,OAAA;YAAAyD,QAAA,EAAM;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN,EACAjC,eAAe,iBACdjC,OAAA;UAAKoD,SAAS,EAAC,2BAA2B;UAAAK,QAAA,gBACxCzD,OAAA;YAAKoD,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEnB,MAAM,EAAE,MAAM;cAAEgD,eAAe,EAAE,SAAS;cAAE5B,YAAY,EAAE,KAAK;cAAE8B,MAAM,EAAE;YAAkB;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClJlE,OAAA;YAAAyD,QAAA,EAAM;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,GAAA,CAxNIR,QAAQ;AAAAyD,GAAA,GAARzD,QAAQ;AA0Nd,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAA0D,GAAA;AAAAC,YAAA,CAAA3D,EAAA;AAAA2D,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}