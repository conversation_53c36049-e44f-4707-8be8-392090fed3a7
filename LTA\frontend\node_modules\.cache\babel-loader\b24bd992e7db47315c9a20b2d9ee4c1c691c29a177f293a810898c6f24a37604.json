{"ast": null, "code": "/**\n * Device Coordinate Integration Utility\n * Handles coordinate capture from any device or mobile phone\n */\n\n/**\n * Get device coordinates using multiple methods with fallbacks\n * @param {Object} options - Configuration options\n * @returns {Promise<Object>} Coordinate data with accuracy and source info\n */\nexport const getDeviceCoordinates = (options = {}) => {\n  const {\n    enableHighAccuracy = true,\n    timeout = 15000,\n    maximumAge = 300000,\n    // 5 minutes\n    fallbackToIP = true\n  } = options;\n  return new Promise((resolve, reject) => {\n    console.log('🌍 Starting device coordinate capture...');\n\n    // Check if geolocation is supported\n    if (!navigator.geolocation) {\n      console.warn('❌ Geolocation not supported by this browser');\n      if (fallbackToIP) {\n        return getIPBasedLocation().then(resolve).catch(reject);\n      }\n      return reject(new Error('Geolocation not supported'));\n    }\n    const geoOptions = {\n      enableHighAccuracy,\n      timeout,\n      maximumAge\n    };\n    console.log('📍 Requesting GPS coordinates with options:', geoOptions);\n    navigator.geolocation.getCurrentPosition(position => {\n      const coords = {\n        latitude: position.coords.latitude,\n        longitude: position.coords.longitude,\n        accuracy: position.coords.accuracy,\n        altitude: position.coords.altitude,\n        altitudeAccuracy: position.coords.altitudeAccuracy,\n        heading: position.coords.heading,\n        speed: position.coords.speed,\n        timestamp: position.timestamp,\n        source: 'GPS',\n        formatted: `${position.coords.latitude.toFixed(6)},${position.coords.longitude.toFixed(6)}`\n      };\n      console.log('✅ GPS coordinates obtained:', coords);\n      resolve(coords);\n    }, error => {\n      console.warn('❌ GPS coordinate error:', error.message);\n\n      // Handle different error types\n      let errorMessage = 'Failed to get location';\n      switch (error.code) {\n        case error.PERMISSION_DENIED:\n          errorMessage = 'Location access denied by user';\n          break;\n        case error.POSITION_UNAVAILABLE:\n          errorMessage = 'Location information unavailable';\n          break;\n        case error.TIMEOUT:\n          errorMessage = 'Location request timed out';\n          break;\n      }\n      console.log(`🔄 GPS failed (${errorMessage}), trying fallback methods...`);\n\n      // Try fallback methods\n      if (fallbackToIP) {\n        getIPBasedLocation().then(resolve).catch(() => reject(new Error(`GPS failed: ${errorMessage}, IP fallback also failed`)));\n      } else {\n        reject(new Error(errorMessage));\n      }\n    }, geoOptions);\n  });\n};\n\n/**\n * Get approximate location based on IP address\n * @returns {Promise<Object>} IP-based coordinate data\n */\nconst getIPBasedLocation = async () => {\n  console.log('🌐 Attempting IP-based location...');\n  try {\n    // Try multiple IP geolocation services\n    const services = ['https://ipapi.co/json/', 'https://ip-api.com/json/', 'https://ipinfo.io/json'];\n    for (const service of services) {\n      try {\n        console.log(`🔄 Trying IP service: ${service}`);\n        const response = await fetch(service, {\n          timeout: 5000\n        });\n        const data = await response.json();\n        let coords = null;\n\n        // Handle different service response formats\n        if (service.includes('ipapi.co')) {\n          coords = {\n            latitude: data.latitude,\n            longitude: data.longitude,\n            city: data.city,\n            region: data.region,\n            country: data.country_name\n          };\n        } else if (service.includes('ip-api.com')) {\n          coords = {\n            latitude: data.lat,\n            longitude: data.lon,\n            city: data.city,\n            region: data.regionName,\n            country: data.country\n          };\n        } else if (service.includes('ipinfo.io')) {\n          const [lat, lng] = (data.loc || '0,0').split(',');\n          coords = {\n            latitude: parseFloat(lat),\n            longitude: parseFloat(lng),\n            city: data.city,\n            region: data.region,\n            country: data.country\n          };\n        }\n        if (coords && coords.latitude && coords.longitude) {\n          const result = {\n            ...coords,\n            accuracy: 10000,\n            // IP-based location is less accurate\n            source: 'IP',\n            service: service,\n            formatted: `${coords.latitude.toFixed(6)},${coords.longitude.toFixed(6)}`,\n            timestamp: Date.now()\n          };\n          console.log('✅ IP-based coordinates obtained:', result);\n          return result;\n        }\n      } catch (serviceError) {\n        console.warn(`❌ IP service ${service} failed:`, serviceError.message);\n        continue;\n      }\n    }\n    throw new Error('All IP geolocation services failed');\n  } catch (error) {\n    console.error('❌ IP-based location failed:', error);\n    throw error;\n  }\n};\n\n/**\n * Watch device position for continuous updates\n * @param {Function} callback - Called with new position data\n * @param {Object} options - Configuration options\n * @returns {number} Watch ID for clearing the watch\n */\nexport const watchDeviceCoordinates = (callback, options = {}) => {\n  const {\n    enableHighAccuracy = true,\n    timeout = 30000,\n    maximumAge = 60000 // 1 minute\n  } = options;\n  if (!navigator.geolocation) {\n    console.warn('❌ Geolocation not supported for watching');\n    return null;\n  }\n  console.log('👀 Starting coordinate watching...');\n  const watchId = navigator.geolocation.watchPosition(position => {\n    const coords = {\n      latitude: position.coords.latitude,\n      longitude: position.coords.longitude,\n      accuracy: position.coords.accuracy,\n      timestamp: position.timestamp,\n      source: 'GPS_WATCH',\n      formatted: `${position.coords.latitude.toFixed(6)},${position.coords.longitude.toFixed(6)}`\n    };\n    console.log('📍 Position update:', coords);\n    callback(coords);\n  }, error => {\n    console.warn('❌ Position watch error:', error.message);\n    callback({\n      error: error.message,\n      source: 'GPS_WATCH_ERROR'\n    });\n  }, {\n    enableHighAccuracy,\n    timeout,\n    maximumAge\n  });\n  return watchId;\n};\n\n/**\n * Clear position watching\n * @param {number} watchId - Watch ID returned by watchDeviceCoordinates\n */\nexport const clearCoordinateWatch = watchId => {\n  if (watchId && navigator.geolocation) {\n    navigator.geolocation.clearWatch(watchId);\n    console.log('🛑 Coordinate watching cleared');\n  }\n};\n\n/**\n * Get coordinate accuracy description\n * @param {number} accuracy - Accuracy in meters\n * @returns {string} Human-readable accuracy description\n */\nexport const getAccuracyDescription = accuracy => {\n  if (!accuracy) return 'Unknown accuracy';\n  if (accuracy <= 5) return 'Very High (±5m)';\n  if (accuracy <= 20) return 'High (±20m)';\n  if (accuracy <= 100) return 'Medium (±100m)';\n  if (accuracy <= 1000) return 'Low (±1km)';\n  return 'Very Low (>1km)';\n};\n\n/**\n * Validate coordinates\n * @param {Object} coords - Coordinate object\n * @returns {boolean} True if coordinates are valid\n */\nexport const validateCoordinates = coords => {\n  if (!coords || typeof coords !== 'object') return false;\n  const {\n    latitude,\n    longitude\n  } = coords;\n  return typeof latitude === 'number' && typeof longitude === 'number' && latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180 && !isNaN(latitude) && !isNaN(longitude);\n};\n\n/**\n * Format coordinates for display\n * @param {Object} coords - Coordinate object\n * @param {number} precision - Decimal places (default: 6)\n * @returns {string} Formatted coordinate string\n */\nexport const formatCoordinates = (coords, precision = 6) => {\n  if (!validateCoordinates(coords)) return 'Invalid coordinates';\n  return `${coords.latitude.toFixed(precision)}, ${coords.longitude.toFixed(precision)}`;\n};\n\n/**\n * Calculate distance between two coordinate points\n * @param {Object} coord1 - First coordinate\n * @param {Object} coord2 - Second coordinate\n * @returns {number} Distance in meters\n */\nexport const calculateDistance = (coord1, coord2) => {\n  if (!validateCoordinates(coord1) || !validateCoordinates(coord2)) {\n    return null;\n  }\n  const R = 6371e3; // Earth's radius in meters\n  const φ1 = coord1.latitude * Math.PI / 180;\n  const φ2 = coord2.latitude * Math.PI / 180;\n  const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;\n  const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;\n  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  return R * c; // Distance in meters\n};", "map": {"version": 3, "names": ["getDeviceCoordinates", "options", "enableHighAccuracy", "timeout", "maximumAge", "fallbackToIP", "Promise", "resolve", "reject", "console", "log", "navigator", "geolocation", "warn", "getIPBasedLocation", "then", "catch", "Error", "geoOptions", "getCurrentPosition", "position", "coords", "latitude", "longitude", "accuracy", "altitude", "altitudeAccuracy", "heading", "speed", "timestamp", "source", "formatted", "toFixed", "error", "message", "errorMessage", "code", "PERMISSION_DENIED", "POSITION_UNAVAILABLE", "TIMEOUT", "services", "service", "response", "fetch", "data", "json", "includes", "city", "region", "country", "country_name", "lat", "lon", "regionName", "lng", "loc", "split", "parseFloat", "result", "Date", "now", "serviceError", "watchDeviceCoordinates", "callback", "watchId", "watchPosition", "clearCoordinateWatch", "clearWatch", "getAccuracyDescription", "validateCoordinates", "isNaN", "formatCoordinates", "precision", "calculateDistance", "coord1", "coord2", "R", "φ1", "Math", "PI", "φ2", "Δφ", "Δλ", "a", "sin", "cos", "c", "atan2", "sqrt"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/utils/deviceCoordinates.js"], "sourcesContent": ["/**\n * Device Coordinate Integration Utility\n * Handles coordinate capture from any device or mobile phone\n */\n\n/**\n * Get device coordinates using multiple methods with fallbacks\n * @param {Object} options - Configuration options\n * @returns {Promise<Object>} Coordinate data with accuracy and source info\n */\nexport const getDeviceCoordinates = (options = {}) => {\n  const {\n    enableHighAccuracy = true,\n    timeout = 15000,\n    maximumAge = 300000, // 5 minutes\n    fallbackToIP = true\n  } = options;\n\n  return new Promise((resolve, reject) => {\n    console.log('🌍 Starting device coordinate capture...');\n\n    // Check if geolocation is supported\n    if (!navigator.geolocation) {\n      console.warn('❌ Geolocation not supported by this browser');\n      if (fallbackToIP) {\n        return getIPBasedLocation().then(resolve).catch(reject);\n      }\n      return reject(new Error('Geolocation not supported'));\n    }\n\n    const geoOptions = {\n      enableHighAccuracy,\n      timeout,\n      maximumAge\n    };\n\n    console.log('📍 Requesting GPS coordinates with options:', geoOptions);\n\n    navigator.geolocation.getCurrentPosition(\n      (position) => {\n        const coords = {\n          latitude: position.coords.latitude,\n          longitude: position.coords.longitude,\n          accuracy: position.coords.accuracy,\n          altitude: position.coords.altitude,\n          altitudeAccuracy: position.coords.altitudeAccuracy,\n          heading: position.coords.heading,\n          speed: position.coords.speed,\n          timestamp: position.timestamp,\n          source: 'GPS',\n          formatted: `${position.coords.latitude.toFixed(6)},${position.coords.longitude.toFixed(6)}`\n        };\n\n        console.log('✅ GPS coordinates obtained:', coords);\n        resolve(coords);\n      },\n      (error) => {\n        console.warn('❌ GPS coordinate error:', error.message);\n        \n        // Handle different error types\n        let errorMessage = 'Failed to get location';\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied by user';\n            break;\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information unavailable';\n            break;\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out';\n            break;\n        }\n\n        console.log(`🔄 GPS failed (${errorMessage}), trying fallback methods...`);\n\n        // Try fallback methods\n        if (fallbackToIP) {\n          getIPBasedLocation()\n            .then(resolve)\n            .catch(() => reject(new Error(`GPS failed: ${errorMessage}, IP fallback also failed`)));\n        } else {\n          reject(new Error(errorMessage));\n        }\n      },\n      geoOptions\n    );\n  });\n};\n\n/**\n * Get approximate location based on IP address\n * @returns {Promise<Object>} IP-based coordinate data\n */\nconst getIPBasedLocation = async () => {\n  console.log('🌐 Attempting IP-based location...');\n  \n  try {\n    // Try multiple IP geolocation services\n    const services = [\n      'https://ipapi.co/json/',\n      'https://ip-api.com/json/',\n      'https://ipinfo.io/json'\n    ];\n\n    for (const service of services) {\n      try {\n        console.log(`🔄 Trying IP service: ${service}`);\n        const response = await fetch(service, { timeout: 5000 });\n        const data = await response.json();\n\n        let coords = null;\n\n        // Handle different service response formats\n        if (service.includes('ipapi.co')) {\n          coords = {\n            latitude: data.latitude,\n            longitude: data.longitude,\n            city: data.city,\n            region: data.region,\n            country: data.country_name\n          };\n        } else if (service.includes('ip-api.com')) {\n          coords = {\n            latitude: data.lat,\n            longitude: data.lon,\n            city: data.city,\n            region: data.regionName,\n            country: data.country\n          };\n        } else if (service.includes('ipinfo.io')) {\n          const [lat, lng] = (data.loc || '0,0').split(',');\n          coords = {\n            latitude: parseFloat(lat),\n            longitude: parseFloat(lng),\n            city: data.city,\n            region: data.region,\n            country: data.country\n          };\n        }\n\n        if (coords && coords.latitude && coords.longitude) {\n          const result = {\n            ...coords,\n            accuracy: 10000, // IP-based location is less accurate\n            source: 'IP',\n            service: service,\n            formatted: `${coords.latitude.toFixed(6)},${coords.longitude.toFixed(6)}`,\n            timestamp: Date.now()\n          };\n\n          console.log('✅ IP-based coordinates obtained:', result);\n          return result;\n        }\n      } catch (serviceError) {\n        console.warn(`❌ IP service ${service} failed:`, serviceError.message);\n        continue;\n      }\n    }\n\n    throw new Error('All IP geolocation services failed');\n  } catch (error) {\n    console.error('❌ IP-based location failed:', error);\n    throw error;\n  }\n};\n\n/**\n * Watch device position for continuous updates\n * @param {Function} callback - Called with new position data\n * @param {Object} options - Configuration options\n * @returns {number} Watch ID for clearing the watch\n */\nexport const watchDeviceCoordinates = (callback, options = {}) => {\n  const {\n    enableHighAccuracy = true,\n    timeout = 30000,\n    maximumAge = 60000 // 1 minute\n  } = options;\n\n  if (!navigator.geolocation) {\n    console.warn('❌ Geolocation not supported for watching');\n    return null;\n  }\n\n  console.log('👀 Starting coordinate watching...');\n\n  const watchId = navigator.geolocation.watchPosition(\n    (position) => {\n      const coords = {\n        latitude: position.coords.latitude,\n        longitude: position.coords.longitude,\n        accuracy: position.coords.accuracy,\n        timestamp: position.timestamp,\n        source: 'GPS_WATCH',\n        formatted: `${position.coords.latitude.toFixed(6)},${position.coords.longitude.toFixed(6)}`\n      };\n\n      console.log('📍 Position update:', coords);\n      callback(coords);\n    },\n    (error) => {\n      console.warn('❌ Position watch error:', error.message);\n      callback({ error: error.message, source: 'GPS_WATCH_ERROR' });\n    },\n    {\n      enableHighAccuracy,\n      timeout,\n      maximumAge\n    }\n  );\n\n  return watchId;\n};\n\n/**\n * Clear position watching\n * @param {number} watchId - Watch ID returned by watchDeviceCoordinates\n */\nexport const clearCoordinateWatch = (watchId) => {\n  if (watchId && navigator.geolocation) {\n    navigator.geolocation.clearWatch(watchId);\n    console.log('🛑 Coordinate watching cleared');\n  }\n};\n\n/**\n * Get coordinate accuracy description\n * @param {number} accuracy - Accuracy in meters\n * @returns {string} Human-readable accuracy description\n */\nexport const getAccuracyDescription = (accuracy) => {\n  if (!accuracy) return 'Unknown accuracy';\n  \n  if (accuracy <= 5) return 'Very High (±5m)';\n  if (accuracy <= 20) return 'High (±20m)';\n  if (accuracy <= 100) return 'Medium (±100m)';\n  if (accuracy <= 1000) return 'Low (±1km)';\n  return 'Very Low (>1km)';\n};\n\n/**\n * Validate coordinates\n * @param {Object} coords - Coordinate object\n * @returns {boolean} True if coordinates are valid\n */\nexport const validateCoordinates = (coords) => {\n  if (!coords || typeof coords !== 'object') return false;\n  \n  const { latitude, longitude } = coords;\n  \n  return (\n    typeof latitude === 'number' &&\n    typeof longitude === 'number' &&\n    latitude >= -90 && latitude <= 90 &&\n    longitude >= -180 && longitude <= 180 &&\n    !isNaN(latitude) && !isNaN(longitude)\n  );\n};\n\n/**\n * Format coordinates for display\n * @param {Object} coords - Coordinate object\n * @param {number} precision - Decimal places (default: 6)\n * @returns {string} Formatted coordinate string\n */\nexport const formatCoordinates = (coords, precision = 6) => {\n  if (!validateCoordinates(coords)) return 'Invalid coordinates';\n  \n  return `${coords.latitude.toFixed(precision)}, ${coords.longitude.toFixed(precision)}`;\n};\n\n/**\n * Calculate distance between two coordinate points\n * @param {Object} coord1 - First coordinate\n * @param {Object} coord2 - Second coordinate\n * @returns {number} Distance in meters\n */\nexport const calculateDistance = (coord1, coord2) => {\n  if (!validateCoordinates(coord1) || !validateCoordinates(coord2)) {\n    return null;\n  }\n\n  const R = 6371e3; // Earth's radius in meters\n  const φ1 = coord1.latitude * Math.PI / 180;\n  const φ2 = coord2.latitude * Math.PI / 180;\n  const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;\n  const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;\n\n  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +\n            Math.cos(φ1) * Math.cos(φ2) *\n            Math.sin(Δλ/2) * Math.sin(Δλ/2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n\n  return R * c; // Distance in meters\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,oBAAoB,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;EACpD,MAAM;IACJC,kBAAkB,GAAG,IAAI;IACzBC,OAAO,GAAG,KAAK;IACfC,UAAU,GAAG,MAAM;IAAE;IACrBC,YAAY,GAAG;EACjB,CAAC,GAAGJ,OAAO;EAEX,OAAO,IAAIK,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtCC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;IAEvD;IACA,IAAI,CAACC,SAAS,CAACC,WAAW,EAAE;MAC1BH,OAAO,CAACI,IAAI,CAAC,6CAA6C,CAAC;MAC3D,IAAIR,YAAY,EAAE;QAChB,OAAOS,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAACR,OAAO,CAAC,CAACS,KAAK,CAACR,MAAM,CAAC;MACzD;MACA,OAAOA,MAAM,CAAC,IAAIS,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACvD;IAEA,MAAMC,UAAU,GAAG;MACjBhB,kBAAkB;MAClBC,OAAO;MACPC;IACF,CAAC;IAEDK,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEQ,UAAU,CAAC;IAEtEP,SAAS,CAACC,WAAW,CAACO,kBAAkB,CACrCC,QAAQ,IAAK;MACZ,MAAMC,MAAM,GAAG;QACbC,QAAQ,EAAEF,QAAQ,CAACC,MAAM,CAACC,QAAQ;QAClCC,SAAS,EAAEH,QAAQ,CAACC,MAAM,CAACE,SAAS;QACpCC,QAAQ,EAAEJ,QAAQ,CAACC,MAAM,CAACG,QAAQ;QAClCC,QAAQ,EAAEL,QAAQ,CAACC,MAAM,CAACI,QAAQ;QAClCC,gBAAgB,EAAEN,QAAQ,CAACC,MAAM,CAACK,gBAAgB;QAClDC,OAAO,EAAEP,QAAQ,CAACC,MAAM,CAACM,OAAO;QAChCC,KAAK,EAAER,QAAQ,CAACC,MAAM,CAACO,KAAK;QAC5BC,SAAS,EAAET,QAAQ,CAACS,SAAS;QAC7BC,MAAM,EAAE,KAAK;QACbC,SAAS,EAAE,GAAGX,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,IAAIZ,QAAQ,CAACC,MAAM,CAACE,SAAS,CAACS,OAAO,CAAC,CAAC,CAAC;MAC3F,CAAC;MAEDvB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEW,MAAM,CAAC;MAClDd,OAAO,CAACc,MAAM,CAAC;IACjB,CAAC,EACAY,KAAK,IAAK;MACTxB,OAAO,CAACI,IAAI,CAAC,yBAAyB,EAAEoB,KAAK,CAACC,OAAO,CAAC;;MAEtD;MACA,IAAIC,YAAY,GAAG,wBAAwB;MAC3C,QAAQF,KAAK,CAACG,IAAI;QAChB,KAAKH,KAAK,CAACI,iBAAiB;UAC1BF,YAAY,GAAG,gCAAgC;UAC/C;QACF,KAAKF,KAAK,CAACK,oBAAoB;UAC7BH,YAAY,GAAG,kCAAkC;UACjD;QACF,KAAKF,KAAK,CAACM,OAAO;UAChBJ,YAAY,GAAG,4BAA4B;UAC3C;MACJ;MAEA1B,OAAO,CAACC,GAAG,CAAC,kBAAkByB,YAAY,+BAA+B,CAAC;;MAE1E;MACA,IAAI9B,YAAY,EAAE;QAChBS,kBAAkB,CAAC,CAAC,CACjBC,IAAI,CAACR,OAAO,CAAC,CACbS,KAAK,CAAC,MAAMR,MAAM,CAAC,IAAIS,KAAK,CAAC,eAAekB,YAAY,2BAA2B,CAAC,CAAC,CAAC;MAC3F,CAAC,MAAM;QACL3B,MAAM,CAAC,IAAIS,KAAK,CAACkB,YAAY,CAAC,CAAC;MACjC;IACF,CAAC,EACDjB,UACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMJ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EACrCL,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EAEjD,IAAI;IACF;IACA,MAAM8B,QAAQ,GAAG,CACf,wBAAwB,EACxB,0BAA0B,EAC1B,wBAAwB,CACzB;IAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;MAC9B,IAAI;QACF/B,OAAO,CAACC,GAAG,CAAC,yBAAyB+B,OAAO,EAAE,CAAC;QAC/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,OAAO,EAAE;UAAEtC,OAAO,EAAE;QAAK,CAAC,CAAC;QACxD,MAAMyC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAIxB,MAAM,GAAG,IAAI;;QAEjB;QACA,IAAIoB,OAAO,CAACK,QAAQ,CAAC,UAAU,CAAC,EAAE;UAChCzB,MAAM,GAAG;YACPC,QAAQ,EAAEsB,IAAI,CAACtB,QAAQ;YACvBC,SAAS,EAAEqB,IAAI,CAACrB,SAAS;YACzBwB,IAAI,EAAEH,IAAI,CAACG,IAAI;YACfC,MAAM,EAAEJ,IAAI,CAACI,MAAM;YACnBC,OAAO,EAAEL,IAAI,CAACM;UAChB,CAAC;QACH,CAAC,MAAM,IAAIT,OAAO,CAACK,QAAQ,CAAC,YAAY,CAAC,EAAE;UACzCzB,MAAM,GAAG;YACPC,QAAQ,EAAEsB,IAAI,CAACO,GAAG;YAClB5B,SAAS,EAAEqB,IAAI,CAACQ,GAAG;YACnBL,IAAI,EAAEH,IAAI,CAACG,IAAI;YACfC,MAAM,EAAEJ,IAAI,CAACS,UAAU;YACvBJ,OAAO,EAAEL,IAAI,CAACK;UAChB,CAAC;QACH,CAAC,MAAM,IAAIR,OAAO,CAACK,QAAQ,CAAC,WAAW,CAAC,EAAE;UACxC,MAAM,CAACK,GAAG,EAAEG,GAAG,CAAC,GAAG,CAACV,IAAI,CAACW,GAAG,IAAI,KAAK,EAAEC,KAAK,CAAC,GAAG,CAAC;UACjDnC,MAAM,GAAG;YACPC,QAAQ,EAAEmC,UAAU,CAACN,GAAG,CAAC;YACzB5B,SAAS,EAAEkC,UAAU,CAACH,GAAG,CAAC;YAC1BP,IAAI,EAAEH,IAAI,CAACG,IAAI;YACfC,MAAM,EAAEJ,IAAI,CAACI,MAAM;YACnBC,OAAO,EAAEL,IAAI,CAACK;UAChB,CAAC;QACH;QAEA,IAAI5B,MAAM,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,SAAS,EAAE;UACjD,MAAMmC,MAAM,GAAG;YACb,GAAGrC,MAAM;YACTG,QAAQ,EAAE,KAAK;YAAE;YACjBM,MAAM,EAAE,IAAI;YACZW,OAAO,EAAEA,OAAO;YAChBV,SAAS,EAAE,GAAGV,MAAM,CAACC,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,IAAIX,MAAM,CAACE,SAAS,CAACS,OAAO,CAAC,CAAC,CAAC,EAAE;YACzEH,SAAS,EAAE8B,IAAI,CAACC,GAAG,CAAC;UACtB,CAAC;UAEDnD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgD,MAAM,CAAC;UACvD,OAAOA,MAAM;QACf;MACF,CAAC,CAAC,OAAOG,YAAY,EAAE;QACrBpD,OAAO,CAACI,IAAI,CAAC,gBAAgB4B,OAAO,UAAU,EAAEoB,YAAY,CAAC3B,OAAO,CAAC;QACrE;MACF;IACF;IAEA,MAAM,IAAIjB,KAAK,CAAC,oCAAoC,CAAC;EACvD,CAAC,CAAC,OAAOgB,KAAK,EAAE;IACdxB,OAAO,CAACwB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6B,sBAAsB,GAAGA,CAACC,QAAQ,EAAE9D,OAAO,GAAG,CAAC,CAAC,KAAK;EAChE,MAAM;IACJC,kBAAkB,GAAG,IAAI;IACzBC,OAAO,GAAG,KAAK;IACfC,UAAU,GAAG,KAAK,CAAC;EACrB,CAAC,GAAGH,OAAO;EAEX,IAAI,CAACU,SAAS,CAACC,WAAW,EAAE;IAC1BH,OAAO,CAACI,IAAI,CAAC,0CAA0C,CAAC;IACxD,OAAO,IAAI;EACb;EAEAJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;EAEjD,MAAMsD,OAAO,GAAGrD,SAAS,CAACC,WAAW,CAACqD,aAAa,CAChD7C,QAAQ,IAAK;IACZ,MAAMC,MAAM,GAAG;MACbC,QAAQ,EAAEF,QAAQ,CAACC,MAAM,CAACC,QAAQ;MAClCC,SAAS,EAAEH,QAAQ,CAACC,MAAM,CAACE,SAAS;MACpCC,QAAQ,EAAEJ,QAAQ,CAACC,MAAM,CAACG,QAAQ;MAClCK,SAAS,EAAET,QAAQ,CAACS,SAAS;MAC7BC,MAAM,EAAE,WAAW;MACnBC,SAAS,EAAE,GAAGX,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,IAAIZ,QAAQ,CAACC,MAAM,CAACE,SAAS,CAACS,OAAO,CAAC,CAAC,CAAC;IAC3F,CAAC;IAEDvB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEW,MAAM,CAAC;IAC1C0C,QAAQ,CAAC1C,MAAM,CAAC;EAClB,CAAC,EACAY,KAAK,IAAK;IACTxB,OAAO,CAACI,IAAI,CAAC,yBAAyB,EAAEoB,KAAK,CAACC,OAAO,CAAC;IACtD6B,QAAQ,CAAC;MAAE9B,KAAK,EAAEA,KAAK,CAACC,OAAO;MAAEJ,MAAM,EAAE;IAAkB,CAAC,CAAC;EAC/D,CAAC,EACD;IACE5B,kBAAkB;IAClBC,OAAO;IACPC;EACF,CACF,CAAC;EAED,OAAO4D,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,GAAIF,OAAO,IAAK;EAC/C,IAAIA,OAAO,IAAIrD,SAAS,CAACC,WAAW,EAAE;IACpCD,SAAS,CAACC,WAAW,CAACuD,UAAU,CAACH,OAAO,CAAC;IACzCvD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0D,sBAAsB,GAAI5C,QAAQ,IAAK;EAClD,IAAI,CAACA,QAAQ,EAAE,OAAO,kBAAkB;EAExC,IAAIA,QAAQ,IAAI,CAAC,EAAE,OAAO,iBAAiB;EAC3C,IAAIA,QAAQ,IAAI,EAAE,EAAE,OAAO,aAAa;EACxC,IAAIA,QAAQ,IAAI,GAAG,EAAE,OAAO,gBAAgB;EAC5C,IAAIA,QAAQ,IAAI,IAAI,EAAE,OAAO,YAAY;EACzC,OAAO,iBAAiB;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6C,mBAAmB,GAAIhD,MAAM,IAAK;EAC7C,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE,OAAO,KAAK;EAEvD,MAAM;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGF,MAAM;EAEtC,OACE,OAAOC,QAAQ,KAAK,QAAQ,IAC5B,OAAOC,SAAS,KAAK,QAAQ,IAC7BD,QAAQ,IAAI,CAAC,EAAE,IAAIA,QAAQ,IAAI,EAAE,IACjCC,SAAS,IAAI,CAAC,GAAG,IAAIA,SAAS,IAAI,GAAG,IACrC,CAAC+C,KAAK,CAAChD,QAAQ,CAAC,IAAI,CAACgD,KAAK,CAAC/C,SAAS,CAAC;AAEzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgD,iBAAiB,GAAGA,CAAClD,MAAM,EAAEmD,SAAS,GAAG,CAAC,KAAK;EAC1D,IAAI,CAACH,mBAAmB,CAAChD,MAAM,CAAC,EAAE,OAAO,qBAAqB;EAE9D,OAAO,GAAGA,MAAM,CAACC,QAAQ,CAACU,OAAO,CAACwC,SAAS,CAAC,KAAKnD,MAAM,CAACE,SAAS,CAACS,OAAO,CAACwC,SAAS,CAAC,EAAE;AACxF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACnD,IAAI,CAACN,mBAAmB,CAACK,MAAM,CAAC,IAAI,CAACL,mBAAmB,CAACM,MAAM,CAAC,EAAE;IAChE,OAAO,IAAI;EACb;EAEA,MAAMC,CAAC,GAAG,MAAM,CAAC,CAAC;EAClB,MAAMC,EAAE,GAAGH,MAAM,CAACpD,QAAQ,GAAGwD,IAAI,CAACC,EAAE,GAAG,GAAG;EAC1C,MAAMC,EAAE,GAAGL,MAAM,CAACrD,QAAQ,GAAGwD,IAAI,CAACC,EAAE,GAAG,GAAG;EAC1C,MAAME,EAAE,GAAG,CAACN,MAAM,CAACrD,QAAQ,GAAGoD,MAAM,CAACpD,QAAQ,IAAIwD,IAAI,CAACC,EAAE,GAAG,GAAG;EAC9D,MAAMG,EAAE,GAAG,CAACP,MAAM,CAACpD,SAAS,GAAGmD,MAAM,CAACnD,SAAS,IAAIuD,IAAI,CAACC,EAAE,GAAG,GAAG;EAEhE,MAAMI,CAAC,GAAGL,IAAI,CAACM,GAAG,CAACH,EAAE,GAAC,CAAC,CAAC,GAAGH,IAAI,CAACM,GAAG,CAACH,EAAE,GAAC,CAAC,CAAC,GAC/BH,IAAI,CAACO,GAAG,CAACR,EAAE,CAAC,GAAGC,IAAI,CAACO,GAAG,CAACL,EAAE,CAAC,GAC3BF,IAAI,CAACM,GAAG,CAACF,EAAE,GAAC,CAAC,CAAC,GAAGJ,IAAI,CAACM,GAAG,CAACF,EAAE,GAAC,CAAC,CAAC;EACzC,MAAMI,CAAC,GAAG,CAAC,GAAGR,IAAI,CAACS,KAAK,CAACT,IAAI,CAACU,IAAI,CAACL,CAAC,CAAC,EAAEL,IAAI,CAACU,IAAI,CAAC,CAAC,GAACL,CAAC,CAAC,CAAC;EAEtD,OAAOP,CAAC,GAAGU,CAAC,CAAC,CAAC;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}