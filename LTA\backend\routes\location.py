"""
Location Services API Routes
Provides endpoints for geocoding, routing, and live tracking
"""

from flask import Blueprint, request, jsonify, session
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
import uuid

from utils.location_services import location_service
from utils.route_tracking import tracking_manager
from config.db import connect_to_db

logger = logging.getLogger(__name__)

location_bp = Blueprint('location', __name__)

@location_bp.route('/geocode', methods=['POST'])
def geocode_address():
    """
    Convert address to coordinates
    
    Request body:
    {
        "address": "Street address to geocode",
        "country_code": "IN" (optional, defaults to IN)
    }
    """
    try:
        data = request.get_json()
        if not data or 'address' not in data:
            return jsonify({
                'success': False,
                'error': 'Address is required'
            }), 400
        
        address = data['address'].strip()
        country_code = data.get('country_code', 'IN')
        
        if not address:
            return jsonify({
                'success': False,
                'error': 'Address cannot be empty'
            }), 400
        
        logger.info(f"Geocoding request for: {address}")
        
        # Perform geocoding
        result = location_service.geocode(address, country_code)
        
        if result:
            return jsonify({
                'success': True,
                'result': {
                    'latitude': result.latitude,
                    'longitude': result.longitude,
                    'address': result.address,
                    'confidence': result.confidence,
                    'source': result.source
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Address not found'
            }), 404
            
    except Exception as e:
        logger.error(f"Geocoding error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@location_bp.route('/reverse-geocode', methods=['POST'])
def reverse_geocode_coordinates():
    """
    Convert coordinates to address
    
    Request body:
    {
        "latitude": 12.9716,
        "longitude": 77.5946
    }
    """
    try:
        data = request.get_json()
        if not data or 'latitude' not in data or 'longitude' not in data:
            return jsonify({
                'success': False,
                'error': 'Latitude and longitude are required'
            }), 400
        
        try:
            latitude = float(data['latitude'])
            longitude = float(data['longitude'])
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'error': 'Invalid latitude or longitude format'
            }), 400
        
        # Validate coordinate ranges
        if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
            return jsonify({
                'success': False,
                'error': 'Invalid coordinate values'
            }), 400
        
        logger.info(f"Reverse geocoding request for: {latitude}, {longitude}")
        
        # Perform reverse geocoding
        result = location_service.reverse_geocode(latitude, longitude)
        
        if result:
            return jsonify({
                'success': True,
                'result': {
                    'latitude': result.latitude,
                    'longitude': result.longitude,
                    'address': result.address,
                    'confidence': result.confidence,
                    'source': result.source
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Address not found for coordinates'
            }), 404
            
    except Exception as e:
        logger.error(f"Reverse geocoding error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@location_bp.route('/route', methods=['POST'])
def get_route():
    """
    Get route between two points
    
    Request body:
    {
        "start": {"latitude": 12.9716, "longitude": 77.5946},
        "end": {"latitude": 12.9352, "longitude": 77.6245},
        "profile": "driving" (optional: driving, walking, cycling)
    }
    """
    try:
        data = request.get_json()
        if not data or 'start' not in data or 'end' not in data:
            return jsonify({
                'success': False,
                'error': 'Start and end coordinates are required'
            }), 400
        
        start = data['start']
        end = data['end']
        profile = data.get('profile', 'driving')
        
        # Validate coordinates
        for point_name, point in [('start', start), ('end', end)]:
            if 'latitude' not in point or 'longitude' not in point:
                return jsonify({
                    'success': False,
                    'error': f'{point_name} point must have latitude and longitude'
                }), 400
            
            try:
                lat = float(point['latitude'])
                lon = float(point['longitude'])
                if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                    raise ValueError("Invalid range")
            except (ValueError, TypeError):
                return jsonify({
                    'success': False,
                    'error': f'Invalid {point_name} coordinates'
                }), 400
        
        start_coords = (float(start['latitude']), float(start['longitude']))
        end_coords = (float(end['latitude']), float(end['longitude']))
        
        logger.info(f"Route request from {start_coords} to {end_coords}")
        
        # Get route
        result = location_service.get_route(start_coords, end_coords, profile)
        
        if result:
            return jsonify({
                'success': True,
                'result': {
                    'coordinates': result.coordinates,
                    'distance_km': result.distance_km,
                    'duration_minutes': result.duration_minutes,
                    'instructions': result.instructions,
                    'source': result.source
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Route not found'
            }), 404
            
    except Exception as e:
        logger.error(f"Routing error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@location_bp.route('/tracking/start', methods=['POST'])
def start_tracking():
    """
    Start a new tracking session
    
    Request body:
    {
        "planned_route": [{"latitude": 12.9716, "longitude": 77.5946}, ...],
        "deviation_threshold": 50.0 (optional, meters)
    }
    """
    try:
        data = request.get_json()
        if not data or 'planned_route' not in data:
            return jsonify({
                'success': False,
                'error': 'Planned route is required'
            }), 400
        
        planned_route_data = data['planned_route']
        deviation_threshold = data.get('deviation_threshold', 50.0)
        
        # Validate and convert route coordinates
        planned_route = []
        for i, point in enumerate(planned_route_data):
            if 'latitude' not in point or 'longitude' not in point:
                return jsonify({
                    'success': False,
                    'error': f'Route point {i} must have latitude and longitude'
                }), 400
            
            try:
                lat = float(point['latitude'])
                lon = float(point['longitude'])
                if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                    raise ValueError("Invalid range")
                planned_route.append((lat, lon))
            except (ValueError, TypeError):
                return jsonify({
                    'success': False,
                    'error': f'Invalid coordinates in route point {i}'
                }), 400
        
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Start tracking
        tracker = tracking_manager.start_tracking(
            session_id, planned_route, deviation_threshold
        )
        
        logger.info(f"Started tracking session {session_id}")
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'deviation_threshold': deviation_threshold,
            'route_points': len(planned_route)
        })
        
    except Exception as e:
        logger.error(f"Start tracking error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@location_bp.route('/tracking/update', methods=['POST'])
def update_tracking():
    """
    Update location for active tracking session
    
    Request body:
    {
        "session_id": "uuid",
        "latitude": 12.9716,
        "longitude": 77.5946,
        "accuracy": 10.0,
        "speed": 5.5 (optional),
        "heading": 45.0 (optional),
        "altitude": 920.0 (optional)
    }
    """
    try:
        data = request.get_json()
        required_fields = ['session_id', 'latitude', 'longitude', 'accuracy']
        
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'{field} is required'
                }), 400
        
        session_id = data['session_id']
        
        # Get tracker
        tracker = tracking_manager.get_tracker(session_id)
        if not tracker:
            return jsonify({
                'success': False,
                'error': 'Tracking session not found'
            }), 404
        
        # Validate coordinates
        try:
            latitude = float(data['latitude'])
            longitude = float(data['longitude'])
            accuracy = float(data['accuracy'])
            
            if not (-90 <= latitude <= 90) or not (-180 <= longitude <= 180):
                raise ValueError("Invalid coordinate range")
            if accuracy < 0:
                raise ValueError("Invalid accuracy")
                
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'error': 'Invalid coordinate or accuracy values'
            }), 400
        
        # Optional fields
        speed = data.get('speed')
        heading = data.get('heading')
        altitude = data.get('altitude')
        
        if speed is not None:
            speed = float(speed)
        if heading is not None:
            heading = float(heading)
        if altitude is not None:
            altitude = float(altitude)
        
        # Update location
        deviation_alert = tracker.update_location(
            latitude, longitude, accuracy,
            speed=speed, heading=heading, altitude=altitude
        )
        
        # Get current stats
        stats = tracker.get_current_stats()
        
        response = {
            'success': True,
            'stats': stats
        }
        
        # Include deviation alert if present
        if deviation_alert:
            response['deviation_alert'] = {
                'timestamp': deviation_alert.timestamp.isoformat(),
                'current_location': deviation_alert.current_location,
                'deviation_distance': deviation_alert.deviation_distance,
                'alert_type': deviation_alert.alert_type,
                'message': deviation_alert.message
            }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Update tracking error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@location_bp.route('/tracking/stop', methods=['POST'])
def stop_tracking():
    """
    Stop tracking session
    
    Request body:
    {
        "session_id": "uuid"
    }
    """
    try:
        data = request.get_json()
        if not data or 'session_id' not in data:
            return jsonify({
                'success': False,
                'error': 'Session ID is required'
            }), 400
        
        session_id = data['session_id']
        
        # Stop tracking
        session_data = tracking_manager.stop_tracking(session_id)
        
        if session_data:
            # Save to database
            db = connect_to_db()
            if db:
                try:
                    # Get tracker to export data
                    tracker_data = tracking_manager.completed_sessions.get(session_id)
                    if tracker_data:
                        # Convert to dict for MongoDB storage
                        session_dict = {
                            'session_id': session_id,
                            'start_time': tracker_data.start_time.isoformat(),
                            'end_time': tracker_data.end_time.isoformat() if tracker_data.end_time else None,
                            'planned_route': tracker_data.planned_route,
                            'total_distance': tracker_data.total_distance,
                            'average_speed': tracker_data.average_speed,
                            'tracking_points_count': len(tracker_data.tracking_points),
                            'deviation_alerts_count': len(tracker_data.deviation_alerts),
                            'status': tracker_data.status,
                            'created_at': datetime.now().isoformat()
                        }
                        
                        db.tracking_sessions.insert_one(session_dict)
                        logger.info(f"Saved tracking session {session_id} to database")
                        
                except Exception as e:
                    logger.error(f"Error saving tracking session to database: {e}")
            
            return jsonify({
                'success': True,
                'session_data': {
                    'session_id': session_id,
                    'total_distance': session_data.total_distance,
                    'average_speed': session_data.average_speed,
                    'duration_minutes': (session_data.end_time - session_data.start_time).total_seconds() / 60,
                    'tracking_points': len(session_data.tracking_points),
                    'deviation_alerts': len(session_data.deviation_alerts)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Tracking session not found'
            }), 404
            
    except Exception as e:
        logger.error(f"Stop tracking error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@location_bp.route('/tracking/status/<session_id>', methods=['GET'])
def get_tracking_status(session_id):
    """Get current tracking status"""
    try:
        tracker = tracking_manager.get_tracker(session_id)
        if not tracker:
            return jsonify({
                'success': False,
                'error': 'Tracking session not found'
            }), 404
        
        stats = tracker.get_current_stats()
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Get tracking status error: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500
