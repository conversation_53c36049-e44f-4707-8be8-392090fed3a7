{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\RoutePlanner.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Row, Col, Button, Alert, Spinner, Badge, Form, Modal, Toast, ToastContainer } from 'react-bootstrap';\nimport { FaRoute, FaClock, FaRoad, FaMapMarkerAlt, FaExchangeAlt, FaPlay, FaStop, FaRedo, FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';\nimport LocationPicker from './LocationPicker';\nimport RouteMap from './RouteMap';\nimport './RoutePlanner.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoutePlanner = ({\n  onRouteReady,\n  disabled = false,\n  initialPickup = null,\n  initialDrop = null\n}) => {\n  _s();\n  const [pickupLocation, setPickupLocation] = useState(initialPickup);\n  const [dropLocation, setDropLocation] = useState(initialDrop);\n  const [route, setRoute] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [routingProfile, setRoutingProfile] = useState('driving');\n\n  // Enhanced navigation states\n  const [isNavigating, setIsNavigating] = useState(false);\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [trackingPoints, setTrackingPoints] = useState([]);\n  const [deviationAlerts, setDeviationAlerts] = useState([]);\n  const [showDeviationModal, setShowDeviationModal] = useState(false);\n  const [currentDeviation, setCurrentDeviation] = useState(null);\n  const [navigationStats, setNavigationStats] = useState({\n    distanceTraveled: 0,\n    timeElapsed: 0,\n    estimatedTimeRemaining: 0,\n    averageSpeed: 0\n  });\n  const [showToast, setShowToast] = useState(false);\n  const [toastMessage, setToastMessage] = useState('');\n  const [toastVariant, setToastVariant] = useState('info');\n\n  // Refs for tracking\n  const watchIdRef = React.useRef(null);\n  const navigationStartTimeRef = React.useRef(null);\n  const lastLocationRef = React.useRef(null);\n\n  // Show toast notification\n  const showNotification = useCallback((message, variant = 'info') => {\n    setToastMessage(message);\n    setToastVariant(variant);\n    setShowToast(true);\n  }, []);\n\n  // Calculate distance between two points (Haversine formula)\n  const calculateDistance = useCallback((lat1, lon1, lat2, lon2) => {\n    const R = 6371; // Earth's radius in kilometers\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c * 1000; // Distance in meters\n  }, []);\n\n  // Check for route deviation\n  const checkRouteDeviation = useCallback((currentLat, currentLon, routeCoordinates) => {\n    if (!routeCoordinates || routeCoordinates.length === 0) return null;\n    let minDistance = Infinity;\n    let closestPointIndex = 0;\n\n    // Find the closest point on the route\n    routeCoordinates.forEach((coord, index) => {\n      const distance = calculateDistance(currentLat, currentLon, coord[1], coord[0]);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestPointIndex = index;\n      }\n    });\n\n    // If deviation is more than 50 meters, trigger alert\n    const DEVIATION_THRESHOLD = 50; // meters\n    if (minDistance > DEVIATION_THRESHOLD) {\n      return {\n        distance: minDistance,\n        closestPoint: routeCoordinates[closestPointIndex],\n        severity: minDistance > 200 ? 'critical' : minDistance > 100 ? 'major' : 'minor'\n      };\n    }\n    return null;\n  }, [calculateDistance]);\n  const generateRoute = useCallback(async () => {\n    if (!pickupLocation || !dropLocation) return;\n    setLoading(true);\n    setError('');\n    try {\n      console.log('🗺️ Generating route...', {\n        pickupLocation,\n        dropLocation,\n        routingProfile\n      });\n      const response = await fetch('/api/location/route', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          start: {\n            latitude: pickupLocation.latitude,\n            longitude: pickupLocation.longitude\n          },\n          end: {\n            latitude: dropLocation.latitude,\n            longitude: dropLocation.longitude\n          },\n          profile: routingProfile\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const routeData = {\n            ...data.result,\n            pickup: pickupLocation,\n            drop: dropLocation,\n            profile: routingProfile\n          };\n          setRoute(routeData);\n          if (onRouteReady) {\n            onRouteReady(routeData);\n          }\n          console.log('✅ Route generated successfully:', routeData);\n        } else {\n          throw new Error(data.error || 'Failed to generate route');\n        }\n      } else {\n        throw new Error(`HTTP ${response.status}: Failed to generate route`);\n      }\n    } catch (error) {\n      console.error('❌ Route generation failed:', error);\n      setError(`Route generation failed: ${error.message}`);\n      setRoute(null);\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [pickupLocation, dropLocation, routingProfile, onRouteReady]);\n\n  // Start navigation with real-time GPS tracking\n  const startNavigation = useCallback(() => {\n    if (!route || !navigator.geolocation) {\n      showNotification('GPS not available or route not generated', 'danger');\n      return;\n    }\n    setIsNavigating(true);\n    setTrackingPoints([]);\n    setDeviationAlerts([]);\n    navigationStartTimeRef.current = Date.now();\n    showNotification('Navigation started! Following your route...', 'success');\n\n    // Start GPS tracking\n    watchIdRef.current = navigator.geolocation.watchPosition(position => {\n      const {\n        latitude,\n        longitude,\n        accuracy\n      } = position.coords;\n      const newLocation = {\n        latitude,\n        longitude,\n        accuracy,\n        timestamp: Date.now()\n      };\n      setCurrentLocation(newLocation);\n      setTrackingPoints(prev => [...prev, newLocation]);\n\n      // Check for route deviation\n      if (route.coordinates) {\n        const deviation = checkRouteDeviation(latitude, longitude, route.coordinates);\n        if (deviation) {\n          const alert = {\n            id: Date.now(),\n            deviation,\n            location: newLocation,\n            timestamp: Date.now()\n          };\n          setDeviationAlerts(prev => [...prev, alert]);\n          setCurrentDeviation(alert);\n          setShowDeviationModal(true);\n          showNotification(`Route deviation detected! ${Math.round(deviation.distance)}m off route`, deviation.severity === 'critical' ? 'danger' : 'warning');\n        }\n      }\n\n      // Update navigation stats\n      if (lastLocationRef.current) {\n        const distance = calculateDistance(lastLocationRef.current.latitude, lastLocationRef.current.longitude, latitude, longitude);\n        setNavigationStats(prev => {\n          const timeElapsed = (Date.now() - navigationStartTimeRef.current) / 1000 / 60; // minutes\n          const newDistanceTraveled = prev.distanceTraveled + distance;\n          const averageSpeed = timeElapsed > 0 ? newDistanceTraveled / 1000 / (timeElapsed / 60) : 0; // km/h\n\n          return {\n            ...prev,\n            distanceTraveled: newDistanceTraveled,\n            timeElapsed,\n            averageSpeed\n          };\n        });\n      }\n      lastLocationRef.current = newLocation;\n    }, error => {\n      console.error('GPS tracking error:', error);\n      showNotification('GPS tracking error. Please check location permissions.', 'danger');\n    }, {\n      enableHighAccuracy: true,\n      timeout: 10000,\n      maximumAge: 1000\n    });\n  }, [route, checkRouteDeviation, calculateDistance, showNotification]);\n\n  // Stop navigation\n  const stopNavigation = useCallback(() => {\n    if (watchIdRef.current) {\n      navigator.geolocation.clearWatch(watchIdRef.current);\n      watchIdRef.current = null;\n    }\n    setIsNavigating(false);\n    setCurrentLocation(null);\n    showNotification('Navigation stopped', 'info');\n  }, [showNotification]);\n\n  // Recalculate route from current location\n  const recalculateRoute = useCallback(async () => {\n    if (!currentLocation || !dropLocation) return;\n    const newPickup = {\n      latitude: currentLocation.latitude,\n      longitude: currentLocation.longitude,\n      address: 'Current Location',\n      name: 'Current Location'\n    };\n    setPickupLocation(newPickup);\n    setShowDeviationModal(false);\n    showNotification('Recalculating route from current location...', 'info');\n\n    // The route will be auto-generated due to the useEffect\n  }, [currentLocation, dropLocation, showNotification]);\n\n  // Auto-generate route when both locations are selected\n  useEffect(() => {\n    if (pickupLocation && dropLocation && !disabled) {\n      generateRoute();\n    } else {\n      setRoute(null);\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    }\n  }, [pickupLocation, dropLocation, routingProfile, disabled, generateRoute, onRouteReady]);\n  const handleSwapLocations = () => {\n    if (disabled) return;\n    const temp = pickupLocation;\n    setPickupLocation(dropLocation);\n    setDropLocation(temp);\n  };\n  const formatDuration = minutes => {\n    if (minutes < 60) {\n      return `${Math.round(minutes)} min`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.round(minutes % 60);\n      return `${hours}h ${mins}m`;\n    }\n  };\n  const formatDistance = km => {\n    if (km < 1) {\n      return `${Math.round(km * 1000)} m`;\n    } else {\n      return `${km.toFixed(1)} km`;\n    }\n  };\n\n  // Debug log to check if component is rendering\n  console.log('RoutePlanner component rendering...', {\n    pickupLocation,\n    dropLocation,\n    route,\n    loading,\n    error\n  });\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"route-planner\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: \"Route Planning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Transportation Mode\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n          value: routingProfile,\n          onChange: e => setRoutingProfile(e.target.value),\n          disabled: disabled,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"driving\",\n            children: \"\\uD83D\\uDE97 Driving\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"walking\",\n            children: \"\\uD83D\\uDEB6 Walking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"cycling\",\n            children: \"\\uD83D\\uDEB4 Cycling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(LocationPicker, {\n            label: \"Pickup Location (From)\",\n            placeholder: \"Enter pickup address or use auto-detect\",\n            onLocationSelect: setPickupLocation,\n            allowAutoDetect: true,\n            allowManualEntry: true,\n            initialLocation: pickupLocation,\n            disabled: disabled\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(LocationPicker, {\n            label: \"Drop Location (To)\",\n            placeholder: \"Enter destination address\",\n            onLocationSelect: setDropLocation,\n            allowAutoDetect: false,\n            allowManualEntry: true,\n            initialLocation: dropLocation,\n            disabled: disabled\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), pickupLocation && dropLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          size: \"sm\",\n          onClick: handleSwapLocations,\n          disabled: disabled,\n          title: \"Swap pickup and drop locations\",\n          children: [/*#__PURE__*/_jsxDEV(FaExchangeAlt, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), \"Swap Locations\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Generating route...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"mb-3\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this), route && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"route-info\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          variant: isNavigating ? \"info\" : \"success\",\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-between mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: isNavigating ? \"Navigation Active\" : \"Route Generated Successfully\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), isNavigating && /*#__PURE__*/_jsxDEV(Badge, {\n              bg: \"success\",\n              className: \"pulse\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this), \"LIVE\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaRoad, {\n                  className: \"me-2 text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-bold\",\n                    children: formatDistance(route.distance_km)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Total Distance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaClock, {\n                  className: \"me-2 text-info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-bold\",\n                    children: formatDuration(route.duration_minutes)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Est. Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n                  className: \"me-2 text-success\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-bold\",\n                    children: route.coordinates.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Route Points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Badge, {\n                  bg: \"secondary\",\n                  className: \"me-2\",\n                  children: route.source.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Routing Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"navigation-controls d-flex gap-2 justify-content-center\",\n            children: [!isNavigating ? /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              onClick: startNavigation,\n              disabled: disabled,\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaPlay, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 21\n              }, this), \"Start Navigation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"danger\",\n              onClick: stopNavigation,\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaStop, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this), \"Stop Navigation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 19\n            }, this), isNavigating && currentLocation && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"warning\",\n              onClick: recalculateRoute,\n              className: \"d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaRedo, {\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 21\n              }, this), \"Recalculate\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this), isNavigating && /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"light\",\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-2\",\n            children: \"Navigation Statistics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold text-primary\",\n                  children: formatDistance(navigationStats.distanceTraveled / 1000)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Traveled\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold text-info\",\n                  children: formatDuration(navigationStats.timeElapsed)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Elapsed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold text-success\",\n                  children: [Math.round(navigationStats.averageSpeed), \" km/h\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Avg Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold text-warning\",\n                  children: deviationAlerts.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Deviations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 17\n          }, this), currentLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Current: \", currentLocation.latitude.toFixed(6), \", \", currentLocation.longitude.toFixed(6), currentLocation.accuracy && ` (±${Math.round(currentLocation.accuracy)}m)`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 15\n        }, this), route.instructions && route.instructions.length > 0 && !isNavigating && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"route-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-2\",\n            children: \"Route Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instructions-list\",\n            style: {\n              maxHeight: '200px',\n              overflowY: 'auto'\n            },\n            children: route.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instruction-item p-2 border-bottom\",\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"light\",\n                text: \"dark\",\n                className: \"me-2\",\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 23\n              }, this), instruction]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 15\n        }, this), deviationAlerts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"deviation-alerts mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-2 text-warning\",\n            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 19\n            }, this), \"Route Deviations (\", deviationAlerts.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: '150px',\n              overflowY: 'auto'\n            },\n            children: deviationAlerts.slice(-5).map((alert, index) => /*#__PURE__*/_jsxDEV(Alert, {\n              variant: alert.deviation.severity === 'critical' ? 'danger' : 'warning',\n              className: \"py-2 mb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [Math.round(alert.deviation.distance), \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 25\n                }, this), \" off route at\", ' ', new Date(alert.timestamp).toLocaleTimeString(), /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: alert.deviation.severity === 'critical' ? 'danger' : 'warning',\n                  className: \"ms-2\",\n                  children: alert.deviation.severity\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 23\n              }, this)\n            }, alert.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this), route && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: /*#__PURE__*/_jsxDEV(RouteMap, {\n          route: route,\n          pickup: pickupLocation,\n          drop: dropLocation,\n          currentLocation: currentLocation,\n          trackingPoints: trackingPoints,\n          deviationAlerts: deviationAlerts,\n          isNavigating: isNavigating,\n          height: \"400px\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 11\n      }, this), route && !loading && !error && !isNavigating && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"info\",\n        className: \"mt-3 mb-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Route Ready!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small\",\n              children: \"Click \\\"Start Navigation\\\" to begin real-time GPS tracking. The system will monitor your path and alert you of any deviations from this planned route.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showDeviationModal,\n      onHide: () => setShowDeviationModal(false),\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        className: \"bg-warning text-dark\",\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), \"Route Deviation Detected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: currentDeviation && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            variant: currentDeviation.deviation.severity === 'critical' ? 'danger' : 'warning',\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"You are off the planned route!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Distance from route:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this), \" \", Math.round(currentDeviation.deviation.distance), \" meters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Severity:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this), ' ', /*#__PURE__*/_jsxDEV(Badge, {\n                bg: currentDeviation.deviation.severity === 'critical' ? 'danger' : 'warning',\n                children: currentDeviation.deviation.severity.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Would you like to recalculate the route from your current location?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowDeviationModal(false),\n          children: \"Continue Current Route\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: recalculateRoute,\n          children: [/*#__PURE__*/_jsxDEV(FaRedo, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this), \"Recalculate Route\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-end\",\n      className: \"p-3\",\n      children: /*#__PURE__*/_jsxDEV(Toast, {\n        show: showToast,\n        onClose: () => setShowToast(false),\n        delay: 4000,\n        autohide: true,\n        bg: toastVariant,\n        children: [/*#__PURE__*/_jsxDEV(Toast.Header, {\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            className: \"me-auto\",\n            children: \"Navigation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toast.Body, {\n          className: toastVariant === 'danger' ? 'text-white' : '',\n          children: toastMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 303,\n    columnNumber: 5\n  }, this);\n};\n_s(RoutePlanner, \"xY5KcNo0tcPx4IzEKpftl093ke8=\");\n_c = RoutePlanner;\nexport default RoutePlanner;\nvar _c;\n$RefreshReg$(_c, \"RoutePlanner\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Card", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Badge", "Form", "Modal", "Toast", "ToastContainer", "FaRoute", "FaClock", "FaRoad", "FaMapMarkerAlt", "FaExchangeAlt", "FaPlay", "FaStop", "FaRedo", "FaExclamationTriangle", "FaCheckCircle", "LocationPicker", "RouteMap", "jsxDEV", "_jsxDEV", "Route<PERSON><PERSON>ner", "onRouteReady", "disabled", "initialPickup", "initialDrop", "_s", "pickupLocation", "setPickupLocation", "dropLocation", "setDropLocation", "route", "setRoute", "loading", "setLoading", "error", "setError", "routingProfile", "setRoutingProfile", "isNavigating", "setIsNavigating", "currentLocation", "setCurrentLocation", "trackingPoints", "setTrackingPoints", "deviationAlerts", "setDeviationAlerts", "showDeviationModal", "setShowDeviationModal", "currentDeviation", "setCurrentDeviation", "navigationStats", "setNavigationStats", "distanceTraveled", "timeElapsed", "estimatedTimeRemaining", "averageSpeed", "showToast", "setShowToast", "toastMessage", "setToastMessage", "toastVariant", "setToastVariant", "watchIdRef", "useRef", "navigationStartTimeRef", "lastLocationRef", "showNotification", "message", "variant", "calculateDistance", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "Math", "PI", "dLon", "a", "sin", "cos", "c", "atan2", "sqrt", "checkRouteDeviation", "currentLat", "currentLon", "routeCoordinates", "length", "minDistance", "Infinity", "closestPointIndex", "for<PERSON>ach", "coord", "index", "distance", "DEVIATION_THRESHOLD", "closestPoint", "severity", "generateRoute", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "start", "latitude", "longitude", "end", "profile", "ok", "data", "json", "success", "routeData", "result", "pickup", "drop", "Error", "status", "startNavigation", "navigator", "geolocation", "current", "Date", "now", "watchPosition", "position", "accuracy", "coords", "newLocation", "timestamp", "prev", "coordinates", "deviation", "alert", "id", "location", "round", "newDistanceTraveled", "enableHighAccuracy", "timeout", "maximumAge", "stopNavigation", "clearWatch", "recalculateRoute", "new<PERSON><PERSON><PERSON>", "address", "name", "handleSwapLocations", "temp", "formatDuration", "minutes", "hours", "floor", "mins", "formatDistance", "km", "toFixed", "className", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "Group", "Label", "Select", "value", "onChange", "e", "target", "md", "label", "placeholder", "onLocationSelect", "allowAutoDetect", "allowManualEntry", "initialLocation", "size", "onClick", "title", "animation", "bg", "sm", "distance_km", "duration_minutes", "source", "toUpperCase", "instructions", "style", "maxHeight", "overflowY", "map", "instruction", "text", "slice", "toLocaleTimeString", "height", "show", "onHide", "centered", "closeButton", "Title", "Footer", "onClose", "delay", "autohide", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/RoutePlanner.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Row, Col, Button, Alert, Spinner, Badge, Form, Modal, Toast, ToastContainer } from 'react-bootstrap';\nimport { FaRoute, FaClock, FaRoad, FaMapMarkerAlt, FaExchangeAlt, FaPlay, FaStop, FaRedo, FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';\nimport LocationPicker from './LocationPicker';\nimport RouteMap from './RouteMap';\nimport './RoutePlanner.css';\n\nconst RoutePlanner = ({ onRouteReady, disabled = false, initialPickup = null, initialDrop = null }) => {\n  const [pickupLocation, setPickupLocation] = useState(initialPickup);\n  const [dropLocation, setDropLocation] = useState(initialDrop);\n  const [route, setRoute] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [routingProfile, setRoutingProfile] = useState('driving');\n\n  // Enhanced navigation states\n  const [isNavigating, setIsNavigating] = useState(false);\n  const [currentLocation, setCurrentLocation] = useState(null);\n  const [trackingPoints, setTrackingPoints] = useState([]);\n  const [deviationAlerts, setDeviationAlerts] = useState([]);\n  const [showDeviationModal, setShowDeviationModal] = useState(false);\n  const [currentDeviation, setCurrentDeviation] = useState(null);\n  const [navigationStats, setNavigationStats] = useState({\n    distanceTraveled: 0,\n    timeElapsed: 0,\n    estimatedTimeRemaining: 0,\n    averageSpeed: 0\n  });\n  const [showToast, setShowToast] = useState(false);\n  const [toastMessage, setToastMessage] = useState('');\n  const [toastVariant, setToastVariant] = useState('info');\n\n  // Refs for tracking\n  const watchIdRef = React.useRef(null);\n  const navigationStartTimeRef = React.useRef(null);\n  const lastLocationRef = React.useRef(null);\n\n  // Show toast notification\n  const showNotification = useCallback((message, variant = 'info') => {\n    setToastMessage(message);\n    setToastVariant(variant);\n    setShowToast(true);\n  }, []);\n\n  // Calculate distance between two points (Haversine formula)\n  const calculateDistance = useCallback((lat1, lon1, lat2, lon2) => {\n    const R = 6371; // Earth's radius in kilometers\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +\n              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n              Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c * 1000; // Distance in meters\n  }, []);\n\n  // Check for route deviation\n  const checkRouteDeviation = useCallback((currentLat, currentLon, routeCoordinates) => {\n    if (!routeCoordinates || routeCoordinates.length === 0) return null;\n\n    let minDistance = Infinity;\n    let closestPointIndex = 0;\n\n    // Find the closest point on the route\n    routeCoordinates.forEach((coord, index) => {\n      const distance = calculateDistance(currentLat, currentLon, coord[1], coord[0]);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestPointIndex = index;\n      }\n    });\n\n    // If deviation is more than 50 meters, trigger alert\n    const DEVIATION_THRESHOLD = 50; // meters\n    if (minDistance > DEVIATION_THRESHOLD) {\n      return {\n        distance: minDistance,\n        closestPoint: routeCoordinates[closestPointIndex],\n        severity: minDistance > 200 ? 'critical' : minDistance > 100 ? 'major' : 'minor'\n      };\n    }\n\n    return null;\n  }, [calculateDistance]);\n\n  const generateRoute = useCallback(async () => {\n    if (!pickupLocation || !dropLocation) return;\n\n    setLoading(true);\n    setError('');\n\n    try {\n      console.log('🗺️ Generating route...', { pickupLocation, dropLocation, routingProfile });\n\n      const response = await fetch('/api/location/route', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          start: {\n            latitude: pickupLocation.latitude,\n            longitude: pickupLocation.longitude\n          },\n          end: {\n            latitude: dropLocation.latitude,\n            longitude: dropLocation.longitude\n          },\n          profile: routingProfile\n        })\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const routeData = {\n            ...data.result,\n            pickup: pickupLocation,\n            drop: dropLocation,\n            profile: routingProfile\n          };\n\n          setRoute(routeData);\n\n          if (onRouteReady) {\n            onRouteReady(routeData);\n          }\n\n          console.log('✅ Route generated successfully:', routeData);\n        } else {\n          throw new Error(data.error || 'Failed to generate route');\n        }\n      } else {\n        throw new Error(`HTTP ${response.status}: Failed to generate route`);\n      }\n    } catch (error) {\n      console.error('❌ Route generation failed:', error);\n      setError(`Route generation failed: ${error.message}`);\n      setRoute(null);\n\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [pickupLocation, dropLocation, routingProfile, onRouteReady]);\n\n  // Start navigation with real-time GPS tracking\n  const startNavigation = useCallback(() => {\n    if (!route || !navigator.geolocation) {\n      showNotification('GPS not available or route not generated', 'danger');\n      return;\n    }\n\n    setIsNavigating(true);\n    setTrackingPoints([]);\n    setDeviationAlerts([]);\n    navigationStartTimeRef.current = Date.now();\n\n    showNotification('Navigation started! Following your route...', 'success');\n\n    // Start GPS tracking\n    watchIdRef.current = navigator.geolocation.watchPosition(\n      (position) => {\n        const { latitude, longitude, accuracy } = position.coords;\n        const newLocation = { latitude, longitude, accuracy, timestamp: Date.now() };\n\n        setCurrentLocation(newLocation);\n        setTrackingPoints(prev => [...prev, newLocation]);\n\n        // Check for route deviation\n        if (route.coordinates) {\n          const deviation = checkRouteDeviation(latitude, longitude, route.coordinates);\n          if (deviation) {\n            const alert = {\n              id: Date.now(),\n              deviation,\n              location: newLocation,\n              timestamp: Date.now()\n            };\n\n            setDeviationAlerts(prev => [...prev, alert]);\n            setCurrentDeviation(alert);\n            setShowDeviationModal(true);\n\n            showNotification(\n              `Route deviation detected! ${Math.round(deviation.distance)}m off route`,\n              deviation.severity === 'critical' ? 'danger' : 'warning'\n            );\n          }\n        }\n\n        // Update navigation stats\n        if (lastLocationRef.current) {\n          const distance = calculateDistance(\n            lastLocationRef.current.latitude,\n            lastLocationRef.current.longitude,\n            latitude,\n            longitude\n          );\n\n          setNavigationStats(prev => {\n            const timeElapsed = (Date.now() - navigationStartTimeRef.current) / 1000 / 60; // minutes\n            const newDistanceTraveled = prev.distanceTraveled + distance;\n            const averageSpeed = timeElapsed > 0 ? (newDistanceTraveled / 1000) / (timeElapsed / 60) : 0; // km/h\n\n            return {\n              ...prev,\n              distanceTraveled: newDistanceTraveled,\n              timeElapsed,\n              averageSpeed\n            };\n          });\n        }\n\n        lastLocationRef.current = newLocation;\n      },\n      (error) => {\n        console.error('GPS tracking error:', error);\n        showNotification('GPS tracking error. Please check location permissions.', 'danger');\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 1000\n      }\n    );\n  }, [route, checkRouteDeviation, calculateDistance, showNotification]);\n\n  // Stop navigation\n  const stopNavigation = useCallback(() => {\n    if (watchIdRef.current) {\n      navigator.geolocation.clearWatch(watchIdRef.current);\n      watchIdRef.current = null;\n    }\n\n    setIsNavigating(false);\n    setCurrentLocation(null);\n    showNotification('Navigation stopped', 'info');\n  }, [showNotification]);\n\n  // Recalculate route from current location\n  const recalculateRoute = useCallback(async () => {\n    if (!currentLocation || !dropLocation) return;\n\n    const newPickup = {\n      latitude: currentLocation.latitude,\n      longitude: currentLocation.longitude,\n      address: 'Current Location',\n      name: 'Current Location'\n    };\n\n    setPickupLocation(newPickup);\n    setShowDeviationModal(false);\n    showNotification('Recalculating route from current location...', 'info');\n\n    // The route will be auto-generated due to the useEffect\n  }, [currentLocation, dropLocation, showNotification]);\n\n  // Auto-generate route when both locations are selected\n  useEffect(() => {\n    if (pickupLocation && dropLocation && !disabled) {\n      generateRoute();\n    } else {\n      setRoute(null);\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    }\n  }, [pickupLocation, dropLocation, routingProfile, disabled, generateRoute, onRouteReady]);\n\n  const handleSwapLocations = () => {\n    if (disabled) return;\n    \n    const temp = pickupLocation;\n    setPickupLocation(dropLocation);\n    setDropLocation(temp);\n  };\n\n  const formatDuration = (minutes) => {\n    if (minutes < 60) {\n      return `${Math.round(minutes)} min`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.round(minutes % 60);\n      return `${hours}h ${mins}m`;\n    }\n  };\n\n  const formatDistance = (km) => {\n    if (km < 1) {\n      return `${Math.round(km * 1000)} m`;\n    } else {\n      return `${km.toFixed(1)} km`;\n    }\n  };\n\n  // Debug log to check if component is rendering\n  console.log('RoutePlanner component rendering...', { pickupLocation, dropLocation, route, loading, error });\n\n  return (\n    <Card className=\"route-planner\">\n      <Card.Header className=\"bg-primary text-white\">\n        <div className=\"d-flex align-items-center\">\n          <FaRoute className=\"me-2\" />\n          <h5 className=\"mb-0\">Route Planning</h5>\n        </div>\n      </Card.Header>\n      \n      <Card.Body>\n        {/* Routing Profile Selection */}\n        <Form.Group className=\"mb-3\">\n          <Form.Label>Transportation Mode</Form.Label>\n          <Form.Select\n            value={routingProfile}\n            onChange={(e) => setRoutingProfile(e.target.value)}\n            disabled={disabled}\n          >\n            <option value=\"driving\">🚗 Driving</option>\n            <option value=\"walking\">🚶 Walking</option>\n            <option value=\"cycling\">🚴 Cycling</option>\n          </Form.Select>\n        </Form.Group>\n\n        <Row>\n          <Col md={6}>\n            {/* Pickup Location */}\n            <LocationPicker\n              label=\"Pickup Location (From)\"\n              placeholder=\"Enter pickup address or use auto-detect\"\n              onLocationSelect={setPickupLocation}\n              allowAutoDetect={true}\n              allowManualEntry={true}\n              initialLocation={pickupLocation}\n              disabled={disabled}\n            />\n          </Col>\n          \n          <Col md={6}>\n            {/* Drop Location */}\n            <LocationPicker\n              label=\"Drop Location (To)\"\n              placeholder=\"Enter destination address\"\n              onLocationSelect={setDropLocation}\n              allowAutoDetect={false}\n              allowManualEntry={true}\n              initialLocation={dropLocation}\n              disabled={disabled}\n            />\n          </Col>\n        </Row>\n\n        {/* Swap Locations Button */}\n        {pickupLocation && dropLocation && (\n          <div className=\"text-center mb-3\">\n            <Button\n              variant=\"outline-secondary\"\n              size=\"sm\"\n              onClick={handleSwapLocations}\n              disabled={disabled}\n              title=\"Swap pickup and drop locations\"\n            >\n              <FaExchangeAlt className=\"me-1\" />\n              Swap Locations\n            </Button>\n          </div>\n        )}\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"text-center py-3\">\n            <Spinner animation=\"border\" className=\"me-2\" />\n            <span>Generating route...</span>\n          </div>\n        )}\n\n        {/* Error Display */}\n        {error && (\n          <Alert variant=\"danger\" className=\"mb-3\">\n            {error}\n          </Alert>\n        )}\n\n        {/* Enhanced Route Information & Navigation Controls */}\n        {route && !loading && (\n          <div className=\"route-info\">\n            <Alert variant={isNavigating ? \"info\" : \"success\"} className=\"mb-3\">\n              <div className=\"d-flex align-items-center justify-content-between mb-2\">\n                <div className=\"d-flex align-items-center\">\n                  <FaRoute className=\"me-2\" />\n                  <strong>{isNavigating ? \"Navigation Active\" : \"Route Generated Successfully\"}</strong>\n                </div>\n                {isNavigating && (\n                  <Badge bg=\"success\" className=\"pulse\">\n                    <FaCheckCircle className=\"me-1\" />\n                    LIVE\n                  </Badge>\n                )}\n              </div>\n\n              <Row className=\"g-3 mb-3\">\n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <FaRoad className=\"me-2 text-primary\" />\n                    <div>\n                      <div className=\"fw-bold\">{formatDistance(route.distance_km)}</div>\n                      <small className=\"text-muted\">Total Distance</small>\n                    </div>\n                  </div>\n                </Col>\n\n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <FaClock className=\"me-2 text-info\" />\n                    <div>\n                      <div className=\"fw-bold\">{formatDuration(route.duration_minutes)}</div>\n                      <small className=\"text-muted\">Est. Duration</small>\n                    </div>\n                  </div>\n                </Col>\n\n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <FaMapMarkerAlt className=\"me-2 text-success\" />\n                    <div>\n                      <div className=\"fw-bold\">{route.coordinates.length}</div>\n                      <small className=\"text-muted\">Route Points</small>\n                    </div>\n                  </div>\n                </Col>\n\n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <Badge bg=\"secondary\" className=\"me-2\">\n                      {route.source.toUpperCase()}\n                    </Badge>\n                    <div>\n                      <small className=\"text-muted\">Routing Service</small>\n                    </div>\n                  </div>\n                </Col>\n              </Row>\n\n              {/* Navigation Controls */}\n              <div className=\"navigation-controls d-flex gap-2 justify-content-center\">\n                {!isNavigating ? (\n                  <Button\n                    variant=\"success\"\n                    onClick={startNavigation}\n                    disabled={disabled}\n                    className=\"d-flex align-items-center\"\n                  >\n                    <FaPlay className=\"me-2\" />\n                    Start Navigation\n                  </Button>\n                ) : (\n                  <Button\n                    variant=\"danger\"\n                    onClick={stopNavigation}\n                    className=\"d-flex align-items-center\"\n                  >\n                    <FaStop className=\"me-2\" />\n                    Stop Navigation\n                  </Button>\n                )}\n\n                {isNavigating && currentLocation && (\n                  <Button\n                    variant=\"warning\"\n                    onClick={recalculateRoute}\n                    className=\"d-flex align-items-center\"\n                  >\n                    <FaRedo className=\"me-2\" />\n                    Recalculate\n                  </Button>\n                )}\n              </div>\n            </Alert>\n\n            {/* Navigation Stats (shown during navigation) */}\n            {isNavigating && (\n              <Alert variant=\"light\" className=\"mb-3\">\n                <h6 className=\"mb-2\">Navigation Statistics</h6>\n                <Row className=\"g-3\">\n                  <Col sm={6} md={3}>\n                    <div className=\"text-center\">\n                      <div className=\"fw-bold text-primary\">{formatDistance(navigationStats.distanceTraveled / 1000)}</div>\n                      <small className=\"text-muted\">Traveled</small>\n                    </div>\n                  </Col>\n                  <Col sm={6} md={3}>\n                    <div className=\"text-center\">\n                      <div className=\"fw-bold text-info\">{formatDuration(navigationStats.timeElapsed)}</div>\n                      <small className=\"text-muted\">Elapsed</small>\n                    </div>\n                  </Col>\n                  <Col sm={6} md={3}>\n                    <div className=\"text-center\">\n                      <div className=\"fw-bold text-success\">{Math.round(navigationStats.averageSpeed)} km/h</div>\n                      <small className=\"text-muted\">Avg Speed</small>\n                    </div>\n                  </Col>\n                  <Col sm={6} md={3}>\n                    <div className=\"text-center\">\n                      <div className=\"fw-bold text-warning\">{deviationAlerts.length}</div>\n                      <small className=\"text-muted\">Deviations</small>\n                    </div>\n                  </Col>\n                </Row>\n\n                {currentLocation && (\n                  <div className=\"mt-2 text-center\">\n                    <small className=\"text-muted\">\n                      Current: {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}\n                      {currentLocation.accuracy && ` (±${Math.round(currentLocation.accuracy)}m)`}\n                    </small>\n                  </div>\n                )}\n              </Alert>\n            )}\n\n            {/* Route Instructions */}\n            {route.instructions && route.instructions.length > 0 && !isNavigating && (\n              <div className=\"route-instructions\">\n                <h6 className=\"mb-2\">Route Instructions</h6>\n                <div className=\"instructions-list\" style={{ maxHeight: '200px', overflowY: 'auto' }}>\n                  {route.instructions.map((instruction, index) => (\n                    <div key={index} className=\"instruction-item p-2 border-bottom\">\n                      <Badge bg=\"light\" text=\"dark\" className=\"me-2\">\n                        {index + 1}\n                      </Badge>\n                      {instruction}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Deviation Alerts */}\n            {deviationAlerts.length > 0 && (\n              <div className=\"deviation-alerts mt-3\">\n                <h6 className=\"mb-2 text-warning\">\n                  <FaExclamationTriangle className=\"me-2\" />\n                  Route Deviations ({deviationAlerts.length})\n                </h6>\n                <div style={{ maxHeight: '150px', overflowY: 'auto' }}>\n                  {deviationAlerts.slice(-5).map((alert, index) => (\n                    <Alert key={alert.id} variant={alert.deviation.severity === 'critical' ? 'danger' : 'warning'} className=\"py-2 mb-2\">\n                      <small>\n                        <strong>{Math.round(alert.deviation.distance)}m</strong> off route at{' '}\n                        {new Date(alert.timestamp).toLocaleTimeString()}\n                        <Badge bg={alert.deviation.severity === 'critical' ? 'danger' : 'warning'} className=\"ms-2\">\n                          {alert.deviation.severity}\n                        </Badge>\n                      </small>\n                    </Alert>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Enhanced Route Map with Real-time Tracking */}\n        {route && (\n          <div className=\"mt-3\">\n            <RouteMap\n              route={route}\n              pickup={pickupLocation}\n              drop={dropLocation}\n              currentLocation={currentLocation}\n              trackingPoints={trackingPoints}\n              deviationAlerts={deviationAlerts}\n              isNavigating={isNavigating}\n              height=\"400px\"\n            />\n          </div>\n        )}\n\n        {/* Ready State */}\n        {route && !loading && !error && !isNavigating && (\n          <Alert variant=\"info\" className=\"mt-3 mb-0\">\n            <div className=\"d-flex align-items-center\">\n              <FaRoute className=\"me-2\" />\n              <div>\n                <strong>Route Ready!</strong>\n                <div className=\"small\">\n                  Click \"Start Navigation\" to begin real-time GPS tracking.\n                  The system will monitor your path and alert you of any deviations from this planned route.\n                </div>\n              </div>\n            </div>\n          </Alert>\n        )}\n      </Card.Body>\n\n      {/* Route Deviation Alert Modal */}\n      <Modal show={showDeviationModal} onHide={() => setShowDeviationModal(false)} centered>\n        <Modal.Header closeButton className=\"bg-warning text-dark\">\n          <Modal.Title>\n            <FaExclamationTriangle className=\"me-2\" />\n            Route Deviation Detected\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {currentDeviation && (\n            <div>\n              <Alert variant={currentDeviation.deviation.severity === 'critical' ? 'danger' : 'warning'}>\n                <h6>You are off the planned route!</h6>\n                <p className=\"mb-2\">\n                  <strong>Distance from route:</strong> {Math.round(currentDeviation.deviation.distance)} meters\n                </p>\n                <p className=\"mb-0\">\n                  <strong>Severity:</strong>{' '}\n                  <Badge bg={currentDeviation.deviation.severity === 'critical' ? 'danger' : 'warning'}>\n                    {currentDeviation.deviation.severity.toUpperCase()}\n                  </Badge>\n                </p>\n              </Alert>\n\n              <p>Would you like to recalculate the route from your current location?</p>\n            </div>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowDeviationModal(false)}>\n            Continue Current Route\n          </Button>\n          <Button variant=\"primary\" onClick={recalculateRoute}>\n            <FaRedo className=\"me-2\" />\n            Recalculate Route\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Toast Notifications */}\n      <ToastContainer position=\"top-end\" className=\"p-3\">\n        <Toast\n          show={showToast}\n          onClose={() => setShowToast(false)}\n          delay={4000}\n          autohide\n          bg={toastVariant}\n        >\n          <Toast.Header>\n            <strong className=\"me-auto\">Navigation</strong>\n          </Toast.Header>\n          <Toast.Body className={toastVariant === 'danger' ? 'text-white' : ''}>\n            {toastMessage}\n          </Toast.Body>\n        </Toast>\n      </ToastContainer>\n    </Card>\n  );\n};\n\nexport default RoutePlanner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,cAAc,QAAQ,iBAAiB;AACnH,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,qBAAqB,EAAEC,aAAa,QAAQ,gBAAgB;AACtJ,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,YAAY;EAAEC,QAAQ,GAAG,KAAK;EAAEC,aAAa,GAAG,IAAI;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrG,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC+B,aAAa,CAAC;EACnE,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAACgC,WAAW,CAAC;EAC7D,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,SAAS,CAAC;;EAE/D;EACA,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC;IACrD4D,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,sBAAsB,EAAE,CAAC;IACzBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,MAAM,CAAC;;EAExD;EACA,MAAMsE,UAAU,GAAGvE,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMC,sBAAsB,GAAGzE,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;EACjD,MAAME,eAAe,GAAG1E,KAAK,CAACwE,MAAM,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAMG,gBAAgB,GAAGxE,WAAW,CAAC,CAACyE,OAAO,EAAEC,OAAO,GAAG,MAAM,KAAK;IAClET,eAAe,CAACQ,OAAO,CAAC;IACxBN,eAAe,CAACO,OAAO,CAAC;IACxBX,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,iBAAiB,GAAG3E,WAAW,CAAC,CAAC4E,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAChE,MAAMC,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,MAAMC,IAAI,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAIM,IAAI,CAACC,EAAE,GAAG,GAAG;IAC1C,MAAMC,IAAI,GAAG,CAACL,IAAI,GAAGF,IAAI,IAAIK,IAAI,CAACC,EAAE,GAAG,GAAG;IAC1C,MAAME,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACL,IAAI,GAAC,CAAC,CAAC,GAAGC,IAAI,CAACI,GAAG,CAACL,IAAI,GAAC,CAAC,CAAC,GACnCC,IAAI,CAACK,GAAG,CAACX,IAAI,GAAGM,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,GAAGD,IAAI,CAACK,GAAG,CAACT,IAAI,GAAGI,IAAI,CAACC,EAAE,GAAG,GAAG,CAAC,GAC/DD,IAAI,CAACI,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC,GAAGF,IAAI,CAACI,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC;IAC7C,MAAMI,CAAC,GAAG,CAAC,GAAGN,IAAI,CAACO,KAAK,CAACP,IAAI,CAACQ,IAAI,CAACL,CAAC,CAAC,EAAEH,IAAI,CAACQ,IAAI,CAAC,CAAC,GAACL,CAAC,CAAC,CAAC;IACtD,OAAOL,CAAC,GAAGQ,CAAC,GAAG,IAAI,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,mBAAmB,GAAG3F,WAAW,CAAC,CAAC4F,UAAU,EAAEC,UAAU,EAAEC,gBAAgB,KAAK;IACpF,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnE,IAAIC,WAAW,GAAGC,QAAQ;IAC1B,IAAIC,iBAAiB,GAAG,CAAC;;IAEzB;IACAJ,gBAAgB,CAACK,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MACzC,MAAMC,QAAQ,GAAG3B,iBAAiB,CAACiB,UAAU,EAAEC,UAAU,EAAEO,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9E,IAAIE,QAAQ,GAAGN,WAAW,EAAE;QAC1BA,WAAW,GAAGM,QAAQ;QACtBJ,iBAAiB,GAAGG,KAAK;MAC3B;IACF,CAAC,CAAC;;IAEF;IACA,MAAME,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAChC,IAAIP,WAAW,GAAGO,mBAAmB,EAAE;MACrC,OAAO;QACLD,QAAQ,EAAEN,WAAW;QACrBQ,YAAY,EAAEV,gBAAgB,CAACI,iBAAiB,CAAC;QACjDO,QAAQ,EAAET,WAAW,GAAG,GAAG,GAAG,UAAU,GAAGA,WAAW,GAAG,GAAG,GAAG,OAAO,GAAG;MAC3E,CAAC;IACH;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAACrB,iBAAiB,CAAC,CAAC;EAEvB,MAAM+B,aAAa,GAAG1G,WAAW,CAAC,YAAY;IAC5C,IAAI,CAACgC,cAAc,IAAI,CAACE,YAAY,EAAE;IAEtCK,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACFkE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;QAAE5E,cAAc;QAAEE,YAAY;QAAEQ;MAAe,CAAC,CAAC;MAExF,MAAMmE,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB,EAAE;QAClDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE;YACLC,QAAQ,EAAErF,cAAc,CAACqF,QAAQ;YACjCC,SAAS,EAAEtF,cAAc,CAACsF;UAC5B,CAAC;UACDC,GAAG,EAAE;YACHF,QAAQ,EAAEnF,YAAY,CAACmF,QAAQ;YAC/BC,SAAS,EAAEpF,YAAY,CAACoF;UAC1B,CAAC;UACDE,OAAO,EAAE9E;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAImE,QAAQ,CAACY,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,MAAMC,SAAS,GAAG;YAChB,GAAGH,IAAI,CAACI,MAAM;YACdC,MAAM,EAAE/F,cAAc;YACtBgG,IAAI,EAAE9F,YAAY;YAClBsF,OAAO,EAAE9E;UACX,CAAC;UAEDL,QAAQ,CAACwF,SAAS,CAAC;UAEnB,IAAIlG,YAAY,EAAE;YAChBA,YAAY,CAACkG,SAAS,CAAC;UACzB;UAEAlB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEiB,SAAS,CAAC;QAC3D,CAAC,MAAM;UACL,MAAM,IAAII,KAAK,CAACP,IAAI,CAAClF,KAAK,IAAI,0BAA0B,CAAC;QAC3D;MACF,CAAC,MAAM;QACL,MAAM,IAAIyF,KAAK,CAAC,QAAQpB,QAAQ,CAACqB,MAAM,4BAA4B,CAAC;MACtE;IACF,CAAC,CAAC,OAAO1F,KAAK,EAAE;MACdmE,OAAO,CAACnE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,4BAA4BD,KAAK,CAACiC,OAAO,EAAE,CAAC;MACrDpC,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIV,YAAY,EAAE;QAChBA,YAAY,CAAC,IAAI,CAAC;MACpB;IACF,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACP,cAAc,EAAEE,YAAY,EAAEQ,cAAc,EAAEf,YAAY,CAAC,CAAC;;EAEhE;EACA,MAAMwG,eAAe,GAAGnI,WAAW,CAAC,MAAM;IACxC,IAAI,CAACoC,KAAK,IAAI,CAACgG,SAAS,CAACC,WAAW,EAAE;MACpC7D,gBAAgB,CAAC,0CAA0C,EAAE,QAAQ,CAAC;MACtE;IACF;IAEA3B,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAAC,EAAE,CAAC;IACtBmB,sBAAsB,CAACgE,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE3ChE,gBAAgB,CAAC,6CAA6C,EAAE,SAAS,CAAC;;IAE1E;IACAJ,UAAU,CAACkE,OAAO,GAAGF,SAAS,CAACC,WAAW,CAACI,aAAa,CACrDC,QAAQ,IAAK;MACZ,MAAM;QAAErB,QAAQ;QAAEC,SAAS;QAAEqB;MAAS,CAAC,GAAGD,QAAQ,CAACE,MAAM;MACzD,MAAMC,WAAW,GAAG;QAAExB,QAAQ;QAAEC,SAAS;QAAEqB,QAAQ;QAAEG,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;MAAE,CAAC;MAE5EzF,kBAAkB,CAAC8F,WAAW,CAAC;MAC/B5F,iBAAiB,CAAC8F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;;MAEjD;MACA,IAAIzG,KAAK,CAAC4G,WAAW,EAAE;QACrB,MAAMC,SAAS,GAAGtD,mBAAmB,CAAC0B,QAAQ,EAAEC,SAAS,EAAElF,KAAK,CAAC4G,WAAW,CAAC;QAC7E,IAAIC,SAAS,EAAE;UACb,MAAMC,KAAK,GAAG;YACZC,EAAE,EAAEZ,IAAI,CAACC,GAAG,CAAC,CAAC;YACdS,SAAS;YACTG,QAAQ,EAAEP,WAAW;YACrBC,SAAS,EAAEP,IAAI,CAACC,GAAG,CAAC;UACtB,CAAC;UAEDrF,kBAAkB,CAAC4F,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEG,KAAK,CAAC,CAAC;UAC5C3F,mBAAmB,CAAC2F,KAAK,CAAC;UAC1B7F,qBAAqB,CAAC,IAAI,CAAC;UAE3BmB,gBAAgB,CACd,6BAA6BU,IAAI,CAACmE,KAAK,CAACJ,SAAS,CAAC3C,QAAQ,CAAC,aAAa,EACxE2C,SAAS,CAACxC,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,SACjD,CAAC;QACH;MACF;;MAEA;MACA,IAAIlC,eAAe,CAAC+D,OAAO,EAAE;QAC3B,MAAMhC,QAAQ,GAAG3B,iBAAiB,CAChCJ,eAAe,CAAC+D,OAAO,CAACjB,QAAQ,EAChC9C,eAAe,CAAC+D,OAAO,CAAChB,SAAS,EACjCD,QAAQ,EACRC,SACF,CAAC;QAED7D,kBAAkB,CAACsF,IAAI,IAAI;UACzB,MAAMpF,WAAW,GAAG,CAAC4E,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlE,sBAAsB,CAACgE,OAAO,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;UAC/E,MAAMgB,mBAAmB,GAAGP,IAAI,CAACrF,gBAAgB,GAAG4C,QAAQ;UAC5D,MAAMzC,YAAY,GAAGF,WAAW,GAAG,CAAC,GAAI2F,mBAAmB,GAAG,IAAI,IAAK3F,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;;UAE9F,OAAO;YACL,GAAGoF,IAAI;YACPrF,gBAAgB,EAAE4F,mBAAmB;YACrC3F,WAAW;YACXE;UACF,CAAC;QACH,CAAC,CAAC;MACJ;MAEAU,eAAe,CAAC+D,OAAO,GAAGO,WAAW;IACvC,CAAC,EACArG,KAAK,IAAK;MACTmE,OAAO,CAACnE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CgC,gBAAgB,CAAC,wDAAwD,EAAE,QAAQ,CAAC;IACtF,CAAC,EACD;MACE+E,kBAAkB,EAAE,IAAI;MACxBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;IACd,CACF,CAAC;EACH,CAAC,EAAE,CAACrH,KAAK,EAAEuD,mBAAmB,EAAEhB,iBAAiB,EAAEH,gBAAgB,CAAC,CAAC;;EAErE;EACA,MAAMkF,cAAc,GAAG1J,WAAW,CAAC,MAAM;IACvC,IAAIoE,UAAU,CAACkE,OAAO,EAAE;MACtBF,SAAS,CAACC,WAAW,CAACsB,UAAU,CAACvF,UAAU,CAACkE,OAAO,CAAC;MACpDlE,UAAU,CAACkE,OAAO,GAAG,IAAI;IAC3B;IAEAzF,eAAe,CAAC,KAAK,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IACxByB,gBAAgB,CAAC,oBAAoB,EAAE,MAAM,CAAC;EAChD,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMoF,gBAAgB,GAAG5J,WAAW,CAAC,YAAY;IAC/C,IAAI,CAAC8C,eAAe,IAAI,CAACZ,YAAY,EAAE;IAEvC,MAAM2H,SAAS,GAAG;MAChBxC,QAAQ,EAAEvE,eAAe,CAACuE,QAAQ;MAClCC,SAAS,EAAExE,eAAe,CAACwE,SAAS;MACpCwC,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE;IACR,CAAC;IAED9H,iBAAiB,CAAC4H,SAAS,CAAC;IAC5BxG,qBAAqB,CAAC,KAAK,CAAC;IAC5BmB,gBAAgB,CAAC,8CAA8C,EAAE,MAAM,CAAC;;IAExE;EACF,CAAC,EAAE,CAAC1B,eAAe,EAAEZ,YAAY,EAAEsC,gBAAgB,CAAC,CAAC;;EAErD;EACAzE,SAAS,CAAC,MAAM;IACd,IAAIiC,cAAc,IAAIE,YAAY,IAAI,CAACN,QAAQ,EAAE;MAC/C8E,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLrE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAIV,YAAY,EAAE;QAChBA,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACK,cAAc,EAAEE,YAAY,EAAEQ,cAAc,EAAEd,QAAQ,EAAE8E,aAAa,EAAE/E,YAAY,CAAC,CAAC;EAEzF,MAAMqI,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIpI,QAAQ,EAAE;IAEd,MAAMqI,IAAI,GAAGjI,cAAc;IAC3BC,iBAAiB,CAACC,YAAY,CAAC;IAC/BC,eAAe,CAAC8H,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE;MAChB,OAAO,GAAGjF,IAAI,CAACmE,KAAK,CAACc,OAAO,CAAC,MAAM;IACrC,CAAC,MAAM;MACL,MAAMC,KAAK,GAAGlF,IAAI,CAACmF,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMG,IAAI,GAAGpF,IAAI,CAACmE,KAAK,CAACc,OAAO,GAAG,EAAE,CAAC;MACrC,OAAO,GAAGC,KAAK,KAAKE,IAAI,GAAG;IAC7B;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,EAAE,IAAK;IAC7B,IAAIA,EAAE,GAAG,CAAC,EAAE;MACV,OAAO,GAAGtF,IAAI,CAACmE,KAAK,CAACmB,EAAE,GAAG,IAAI,CAAC,IAAI;IACrC,CAAC,MAAM;MACL,OAAO,GAAGA,EAAE,CAACC,OAAO,CAAC,CAAC,CAAC,KAAK;IAC9B;EACF,CAAC;;EAED;EACA9D,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;IAAE5E,cAAc;IAAEE,YAAY;IAAEE,KAAK;IAAEE,OAAO;IAAEE;EAAM,CAAC,CAAC;EAE3G,oBACEf,OAAA,CAACxB,IAAI;IAACyK,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC7BlJ,OAAA,CAACxB,IAAI,CAAC2K,MAAM;MAACF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5ClJ,OAAA;QAAKiJ,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxClJ,OAAA,CAACb,OAAO;UAAC8J,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BvJ,OAAA;UAAIiJ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdvJ,OAAA,CAACxB,IAAI,CAACgL,IAAI;MAAAN,QAAA,gBAERlJ,OAAA,CAACjB,IAAI,CAAC0K,KAAK;QAACR,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAC1BlJ,OAAA,CAACjB,IAAI,CAAC2K,KAAK;UAAAR,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5CvJ,OAAA,CAACjB,IAAI,CAAC4K,MAAM;UACVC,KAAK,EAAE3I,cAAe;UACtB4I,QAAQ,EAAGC,CAAC,IAAK5I,iBAAiB,CAAC4I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACnDzJ,QAAQ,EAAEA,QAAS;UAAA+I,QAAA,gBAEnBlJ,OAAA;YAAQ4J,KAAK,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3CvJ,OAAA;YAAQ4J,KAAK,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3CvJ,OAAA;YAAQ4J,KAAK,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEbvJ,OAAA,CAACvB,GAAG;QAAAyK,QAAA,gBACFlJ,OAAA,CAACtB,GAAG;UAACsL,EAAE,EAAE,CAAE;UAAAd,QAAA,eAETlJ,OAAA,CAACH,cAAc;YACboK,KAAK,EAAC,wBAAwB;YAC9BC,WAAW,EAAC,yCAAyC;YACrDC,gBAAgB,EAAE3J,iBAAkB;YACpC4J,eAAe,EAAE,IAAK;YACtBC,gBAAgB,EAAE,IAAK;YACvBC,eAAe,EAAE/J,cAAe;YAChCJ,QAAQ,EAAEA;UAAS;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvJ,OAAA,CAACtB,GAAG;UAACsL,EAAE,EAAE,CAAE;UAAAd,QAAA,eAETlJ,OAAA,CAACH,cAAc;YACboK,KAAK,EAAC,oBAAoB;YAC1BC,WAAW,EAAC,2BAA2B;YACvCC,gBAAgB,EAAEzJ,eAAgB;YAClC0J,eAAe,EAAE,KAAM;YACvBC,gBAAgB,EAAE,IAAK;YACvBC,eAAe,EAAE7J,YAAa;YAC9BN,QAAQ,EAAEA;UAAS;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLhJ,cAAc,IAAIE,YAAY,iBAC7BT,OAAA;QAAKiJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BlJ,OAAA,CAACrB,MAAM;UACLsE,OAAO,EAAC,mBAAmB;UAC3BsH,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEjC,mBAAoB;UAC7BpI,QAAQ,EAAEA,QAAS;UACnBsK,KAAK,EAAC,gCAAgC;UAAAvB,QAAA,gBAEtClJ,OAAA,CAACT,aAAa;YAAC0J,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA1I,OAAO,iBACNb,OAAA;QAAKiJ,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlJ,OAAA,CAACnB,OAAO;UAAC6L,SAAS,EAAC,QAAQ;UAACzB,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CvJ,OAAA;UAAAkJ,QAAA,EAAM;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACN,EAGAxI,KAAK,iBACJf,OAAA,CAACpB,KAAK;QAACqE,OAAO,EAAC,QAAQ;QAACgG,SAAS,EAAC,MAAM;QAAAC,QAAA,EACrCnI;MAAK;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA5I,KAAK,IAAI,CAACE,OAAO,iBAChBb,OAAA;QAAKiJ,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlJ,OAAA,CAACpB,KAAK;UAACqE,OAAO,EAAE9B,YAAY,GAAG,MAAM,GAAG,SAAU;UAAC8H,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjElJ,OAAA;YAAKiJ,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACrElJ,OAAA;cAAKiJ,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClJ,OAAA,CAACb,OAAO;gBAAC8J,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5BvJ,OAAA;gBAAAkJ,QAAA,EAAS/H,YAAY,GAAG,mBAAmB,GAAG;cAA8B;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACLpI,YAAY,iBACXnB,OAAA,CAAClB,KAAK;cAAC6L,EAAE,EAAC,SAAS;cAAC1B,SAAS,EAAC,OAAO;cAAAC,QAAA,gBACnClJ,OAAA,CAACJ,aAAa;gBAACqJ,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAEpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENvJ,OAAA,CAACvB,GAAG;YAACwK,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAACX,MAAM;kBAAC4J,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxCvJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAKiJ,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEJ,cAAc,CAACnI,KAAK,CAACkK,WAAW;kBAAC;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEvJ,OAAA;oBAAOiJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAACZ,OAAO;kBAAC6J,SAAS,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCvJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAKiJ,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAET,cAAc,CAAC9H,KAAK,CAACmK,gBAAgB;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEvJ,OAAA;oBAAOiJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAACV,cAAc;kBAAC2J,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDvJ,OAAA;kBAAAkJ,QAAA,gBACElJ,OAAA;oBAAKiJ,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEvI,KAAK,CAAC4G,WAAW,CAACjD;kBAAM;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzDvJ,OAAA;oBAAOiJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxClJ,OAAA,CAAClB,KAAK;kBAAC6L,EAAE,EAAC,WAAW;kBAAC1B,SAAS,EAAC,MAAM;kBAAAC,QAAA,EACnCvI,KAAK,CAACoK,MAAM,CAACC,WAAW,CAAC;gBAAC;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACRvJ,OAAA;kBAAAkJ,QAAA,eACElJ,OAAA;oBAAOiJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvJ,OAAA;YAAKiJ,SAAS,EAAC,yDAAyD;YAAAC,QAAA,GACrE,CAAC/H,YAAY,gBACZnB,OAAA,CAACrB,MAAM;cACLsE,OAAO,EAAC,SAAS;cACjBuH,OAAO,EAAE9D,eAAgB;cACzBvG,QAAQ,EAAEA,QAAS;cACnB8I,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBAErClJ,OAAA,CAACR,MAAM;gBAACyJ,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAETvJ,OAAA,CAACrB,MAAM;cACLsE,OAAO,EAAC,QAAQ;cAChBuH,OAAO,EAAEvC,cAAe;cACxBgB,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBAErClJ,OAAA,CAACP,MAAM;gBAACwJ,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEApI,YAAY,IAAIE,eAAe,iBAC9BrB,OAAA,CAACrB,MAAM;cACLsE,OAAO,EAAC,SAAS;cACjBuH,OAAO,EAAErC,gBAAiB;cAC1Bc,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBAErClJ,OAAA,CAACN,MAAM;gBAACuJ,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGPpI,YAAY,iBACXnB,OAAA,CAACpB,KAAK;UAACqE,OAAO,EAAC,OAAO;UAACgG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACrClJ,OAAA;YAAIiJ,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CvJ,OAAA,CAACvB,GAAG;YAACwK,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBlJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlJ,OAAA;kBAAKiJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAEJ,cAAc,CAAC/G,eAAe,CAACE,gBAAgB,GAAG,IAAI;gBAAC;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrGvJ,OAAA;kBAAOiJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlJ,OAAA;kBAAKiJ,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAET,cAAc,CAAC1G,eAAe,CAACG,WAAW;gBAAC;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtFvJ,OAAA;kBAAOiJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlJ,OAAA;kBAAKiJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAEzF,IAAI,CAACmE,KAAK,CAAC7F,eAAe,CAACK,YAAY,CAAC,EAAC,OAAK;gBAAA;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3FvJ,OAAA;kBAAOiJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvJ,OAAA,CAACtB,GAAG;cAACkM,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChBlJ,OAAA;gBAAKiJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlJ,OAAA;kBAAKiJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAEzH,eAAe,CAAC6C;gBAAM;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpEvJ,OAAA;kBAAOiJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELlI,eAAe,iBACdrB,OAAA;YAAKiJ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BlJ,OAAA;cAAOiJ,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,WACnB,EAAC7H,eAAe,CAACuE,QAAQ,CAACoD,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAAC3H,eAAe,CAACwE,SAAS,CAACmD,OAAO,CAAC,CAAC,CAAC,EACpF3H,eAAe,CAAC6F,QAAQ,IAAI,MAAMzD,IAAI,CAACmE,KAAK,CAACvG,eAAe,CAAC6F,QAAQ,CAAC,IAAI;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA5I,KAAK,CAACsK,YAAY,IAAItK,KAAK,CAACsK,YAAY,CAAC3G,MAAM,GAAG,CAAC,IAAI,CAACnD,YAAY,iBACnEnB,OAAA;UAAKiJ,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjClJ,OAAA;YAAIiJ,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CvJ,OAAA;YAAKiJ,SAAS,EAAC,mBAAmB;YAACiC,KAAK,EAAE;cAAEC,SAAS,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAAlC,QAAA,EACjFvI,KAAK,CAACsK,YAAY,CAACI,GAAG,CAAC,CAACC,WAAW,EAAE1G,KAAK,kBACzC5E,OAAA;cAAiBiJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBAC7DlJ,OAAA,CAAClB,KAAK;gBAAC6L,EAAE,EAAC,OAAO;gBAACY,IAAI,EAAC,MAAM;gBAACtC,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAC3CtE,KAAK,GAAG;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACP+B,WAAW;YAAA,GAJJ1G,KAAK;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA9H,eAAe,CAAC6C,MAAM,GAAG,CAAC,iBACzBtE,OAAA;UAAKiJ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpClJ,OAAA;YAAIiJ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC/BlJ,OAAA,CAACL,qBAAqB;cAACsJ,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBACxB,EAAC9H,eAAe,CAAC6C,MAAM,EAAC,GAC5C;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvJ,OAAA;YAAKkL,KAAK,EAAE;cAAEC,SAAS,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAAlC,QAAA,EACnDzH,eAAe,CAAC+J,KAAK,CAAC,CAAC,CAAC,CAAC,CAACH,GAAG,CAAC,CAAC5D,KAAK,EAAE7C,KAAK,kBAC1C5E,OAAA,CAACpB,KAAK;cAAgBqE,OAAO,EAAEwE,KAAK,CAACD,SAAS,CAACxC,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,SAAU;cAACiE,SAAS,EAAC,WAAW;cAAAC,QAAA,eAClHlJ,OAAA;gBAAAkJ,QAAA,gBACElJ,OAAA;kBAAAkJ,QAAA,GAASzF,IAAI,CAACmE,KAAK,CAACH,KAAK,CAACD,SAAS,CAAC3C,QAAQ,CAAC,EAAC,GAAC;gBAAA;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,iBAAa,EAAC,GAAG,EACxE,IAAIzC,IAAI,CAACW,KAAK,CAACJ,SAAS,CAAC,CAACoE,kBAAkB,CAAC,CAAC,eAC/CzL,OAAA,CAAClB,KAAK;kBAAC6L,EAAE,EAAElD,KAAK,CAACD,SAAS,CAACxC,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,SAAU;kBAACiE,SAAS,EAAC,MAAM;kBAAAC,QAAA,EACxFzB,KAAK,CAACD,SAAS,CAACxC;gBAAQ;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAPE9B,KAAK,CAACC,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQb,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA5I,KAAK,iBACJX,OAAA;QAAKiJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBlJ,OAAA,CAACF,QAAQ;UACPa,KAAK,EAAEA,KAAM;UACb2F,MAAM,EAAE/F,cAAe;UACvBgG,IAAI,EAAE9F,YAAa;UACnBY,eAAe,EAAEA,eAAgB;UACjCE,cAAc,EAAEA,cAAe;UAC/BE,eAAe,EAAEA,eAAgB;UACjCN,YAAY,EAAEA,YAAa;UAC3BuK,MAAM,EAAC;QAAO;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGA5I,KAAK,IAAI,CAACE,OAAO,IAAI,CAACE,KAAK,IAAI,CAACI,YAAY,iBAC3CnB,OAAA,CAACpB,KAAK;QAACqE,OAAO,EAAC,MAAM;QAACgG,SAAS,EAAC,WAAW;QAAAC,QAAA,eACzClJ,OAAA;UAAKiJ,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxClJ,OAAA,CAACb,OAAO;YAAC8J,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BvJ,OAAA;YAAAkJ,QAAA,gBACElJ,OAAA;cAAAkJ,QAAA,EAAQ;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7BvJ,OAAA;cAAKiJ,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAGvB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAGZvJ,OAAA,CAAChB,KAAK;MAAC2M,IAAI,EAAEhK,kBAAmB;MAACiK,MAAM,EAAEA,CAAA,KAAMhK,qBAAqB,CAAC,KAAK,CAAE;MAACiK,QAAQ;MAAA3C,QAAA,gBACnFlJ,OAAA,CAAChB,KAAK,CAACmK,MAAM;QAAC2C,WAAW;QAAC7C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACxDlJ,OAAA,CAAChB,KAAK,CAAC+M,KAAK;UAAA7C,QAAA,gBACVlJ,OAAA,CAACL,qBAAqB;YAACsJ,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfvJ,OAAA,CAAChB,KAAK,CAACwK,IAAI;QAAAN,QAAA,EACRrH,gBAAgB,iBACf7B,OAAA;UAAAkJ,QAAA,gBACElJ,OAAA,CAACpB,KAAK;YAACqE,OAAO,EAAEpB,gBAAgB,CAAC2F,SAAS,CAACxC,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,SAAU;YAAAkE,QAAA,gBACxFlJ,OAAA;cAAAkJ,QAAA,EAAI;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCvJ,OAAA;cAAGiJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBlJ,OAAA;gBAAAkJ,QAAA,EAAQ;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC9F,IAAI,CAACmE,KAAK,CAAC/F,gBAAgB,CAAC2F,SAAS,CAAC3C,QAAQ,CAAC,EAAC,SACzF;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvJ,OAAA;cAAGiJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBlJ,OAAA;gBAAAkJ,QAAA,EAAQ;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAAC,GAAG,eAC9BvJ,OAAA,CAAClB,KAAK;gBAAC6L,EAAE,EAAE9I,gBAAgB,CAAC2F,SAAS,CAACxC,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,SAAU;gBAAAkE,QAAA,EAClFrH,gBAAgB,CAAC2F,SAAS,CAACxC,QAAQ,CAACgG,WAAW,CAAC;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAERvJ,OAAA;YAAAkJ,QAAA,EAAG;UAAmE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbvJ,OAAA,CAAChB,KAAK,CAACgN,MAAM;QAAA9C,QAAA,gBACXlJ,OAAA,CAACrB,MAAM;UAACsE,OAAO,EAAC,WAAW;UAACuH,OAAO,EAAEA,CAAA,KAAM5I,qBAAqB,CAAC,KAAK,CAAE;UAAAsH,QAAA,EAAC;QAEzE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvJ,OAAA,CAACrB,MAAM;UAACsE,OAAO,EAAC,SAAS;UAACuH,OAAO,EAAErC,gBAAiB;UAAAe,QAAA,gBAClDlJ,OAAA,CAACN,MAAM;YAACuJ,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRvJ,OAAA,CAACd,cAAc;MAAC+H,QAAQ,EAAC,SAAS;MAACgC,SAAS,EAAC,KAAK;MAAAC,QAAA,eAChDlJ,OAAA,CAACf,KAAK;QACJ0M,IAAI,EAAEtJ,SAAU;QAChB4J,OAAO,EAAEA,CAAA,KAAM3J,YAAY,CAAC,KAAK,CAAE;QACnC4J,KAAK,EAAE,IAAK;QACZC,QAAQ;QACRxB,EAAE,EAAElI,YAAa;QAAAyG,QAAA,gBAEjBlJ,OAAA,CAACf,KAAK,CAACkK,MAAM;UAAAD,QAAA,eACXlJ,OAAA;YAAQiJ,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACfvJ,OAAA,CAACf,KAAK,CAACuK,IAAI;UAACP,SAAS,EAAExG,YAAY,KAAK,QAAQ,GAAG,YAAY,GAAG,EAAG;UAAAyG,QAAA,EAClE3G;QAAY;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEX,CAAC;AAACjJ,EAAA,CAxoBIL,YAAY;AAAAmM,EAAA,GAAZnM,YAAY;AA0oBlB,eAAeA,YAAY;AAAC,IAAAmM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}