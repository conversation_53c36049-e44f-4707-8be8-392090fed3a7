{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\VideoDefectDetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col, Tab, Tabs, Badge } from 'react-bootstrap';\nimport axios from 'axios';\nimport Webcam from 'react-webcam';\nimport useResponsive from '../hooks/useResponsive';\nimport './VideoDefectDetection.css';\nimport { validateUploadFile, showFileValidationError } from '../utils/fileValidation';\nimport RoutePlanner from './RoutePlanner';\nimport LiveTracking from './LiveTracking';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoDefectDetection = () => {\n  _s();\n  const [selectedModel, setSelectedModel] = useState('All');\n  const [videoFile, setVideoFile] = useState(null);\n  const [videoPreview, setVideoPreview] = useState(null);\n  const [processedVideo, setProcessedVideo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [shouldStop, setShouldStop] = useState(false);\n  const [coordinates, setCoordinates] = useState('Not Available');\n  const [inputSource, setInputSource] = useState('video');\n  const [cameraActive, setCameraActive] = useState(false);\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [recordedChunks, setRecordedChunks] = useState([]);\n  const recordedChunksRef = useRef([]); // <-- Add this line\n\n  // Location and routing states\n  const [activeTab, setActiveTab] = useState('detection');\n  const [plannedRoute, setPlannedRoute] = useState(null);\n  const [trackingSession, setTrackingSession] = useState(null);\n  const [deviationAlerts, setDeviationAlerts] = useState([]);\n  const [locationEnabled, setLocationEnabled] = useState(false);\n\n  // Video processing states\n  const [frameBuffer, setFrameBuffer] = useState([]);\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\n  const [isBuffering, setIsBuffering] = useState(false);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [processingProgress, setProcessingProgress] = useState(0);\n  const [allDetections, setAllDetections] = useState([]);\n  const [videoResults, setVideoResults] = useState(null);\n\n  // Add new state for frame-by-frame updates\n  const [currentDetections, setCurrentDetections] = useState([]);\n  const [showResults, setShowResults] = useState(false);\n  const streamRef = useRef(null);\n  const webcamRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const recordingTimerRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const {\n    isMobile\n  } = useResponsive();\n\n  // const BUFFER_SIZE = 10; // Unused for now\n  const PLAYBACK_FPS = 15;\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\n\n  const [totalFrames, setTotalFrames] = useState(null);\n  const totalFramesValid = Number.isFinite(totalFrames) && totalFrames > 0;\n  const [videoDuration, setVideoDuration] = useState(null);\n  const [videoFPS, setVideoFPS] = useState(30); // Default FPS\n\n  // Available models\n  const modelOptions = [{\n    value: 'All',\n    label: 'All (detect all types of defects)'\n  }, {\n    value: 'Potholes',\n    label: 'Potholes'\n  }, {\n    value: 'Alligator Cracks',\n    label: 'Alligator Cracks'\n  }, {\n    value: 'Kerbs',\n    label: 'Kerbs'\n  }];\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        const {\n          latitude,\n          longitude\n        } = position.coords;\n        setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\n      }, err => {\n        console.error(\"Error getting location:\", err);\n        setCoordinates('Location unavailable');\n      });\n    }\n  }, []);\n\n  // Recording timer\n  useEffect(() => {\n    if (isRecording) {\n      recordingTimerRef.current = setInterval(() => {\n        setRecordingTime(prev => {\n          if (prev >= MAX_RECORDING_TIME) {\n            handleStopRecording();\n            return MAX_RECORDING_TIME;\n          }\n          return prev + 1;\n        });\n      }, 1000);\n    } else {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    }\n    return () => {\n      if (recordingTimerRef.current) {\n        clearInterval(recordingTimerRef.current);\n      }\n    };\n  }, [isRecording, handleStopRecording, MAX_RECORDING_TIME]);\n\n  // Video playback effect\n  useEffect(() => {\n    let playbackInterval;\n    if (isPlaying && frameBuffer.length > 0) {\n      playbackInterval = setInterval(() => {\n        setCurrentFrameIndex(prev => {\n          if (prev < frameBuffer.length - 1) {\n            return prev + 1;\n          } else {\n            setIsPlaying(false);\n            return prev;\n          }\n        });\n      }, 1000 / PLAYBACK_FPS);\n    }\n    return () => {\n      if (playbackInterval) clearInterval(playbackInterval);\n    };\n  }, [isPlaying, frameBuffer]);\n\n  // Update processed video when frame changes\n  useEffect(() => {\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\n    }\n  }, [currentFrameIndex, frameBuffer]);\n\n  // When videoPreview is set, extract duration and FPS\n  useEffect(() => {\n    if (videoPreview) {\n      const video = document.createElement('video');\n      video.src = videoPreview;\n      video.preload = 'metadata';\n      video.onloadedmetadata = () => {\n        setVideoDuration(video.duration);\n        // Try to get FPS from video tracks if available\n        if (video.webkitVideoDecodedByteCount !== undefined) {\n          // Not standard, but some browsers may expose frameRate\n          try {\n            const tracks = video.videoTracks || video.captureStream && video.captureStream().getVideoTracks();\n            if (tracks && tracks.length > 0 && tracks[0].getSettings) {\n              const settings = tracks[0].getSettings();\n              if (settings.frameRate) {\n                setVideoFPS(settings.frameRate);\n              }\n            }\n          } catch (e) {}\n        }\n      };\n    }\n  }, [videoPreview]);\n\n  // Helper to estimate total frames if backend total_frames is invalid\n  const estimatedTotalFrames = videoDuration && videoFPS ? Math.round(videoDuration * videoFPS) : null;\n\n  // Add cleanup effect for SSE stream\n  useEffect(() => {\n    return () => {\n      if (streamRef.current) {\n        streamRef.current.abort();\n      }\n    };\n  }, []);\n  const [warning, setWarning] = useState('');\n\n  // Handle video file selection\n  const handleVideoChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate video file first\n      const validation = validateUploadFile(file, 'video', 'video_defect_detection');\n      if (!validation.isValid) {\n        showFileValidationError(validation.errorMessage, setError);\n        // Clear the file input\n        if (e.target) {\n          e.target.value = '';\n        }\n        return;\n      }\n\n      // Clear any previous errors\n      setError('');\n      const video = document.createElement('video');\n      video.preload = 'metadata';\n      video.onloadedmetadata = () => {\n        if (video.duration > 60) {\n          setWarning('Video upload is restricted to 1 minute. Please select a shorter video.');\n          setVideoFile(null);\n          setVideoPreview(null);\n          setProcessedVideo(null);\n          setVideoResults(null);\n          setAllDetections([]);\n          setError('');\n          if (fileInputRef.current) fileInputRef.current.value = '';\n        } else {\n          setWarning('');\n          setVideoFile(file);\n          setVideoPreview(URL.createObjectURL(file));\n          setProcessedVideo(null);\n          setVideoResults(null);\n          setAllDetections([]);\n          setError('');\n        }\n      };\n      video.src = URL.createObjectURL(file);\n    }\n  };\n\n  // Handle camera activation\n  const toggleCamera = () => {\n    setCameraActive(!cameraActive);\n    if (!cameraActive) {\n      setVideoFile(null);\n      setVideoPreview(null);\n      setProcessedVideo(null);\n      setVideoResults(null);\n      setAllDetections([]);\n      setError('');\n    }\n  };\n\n  // Start recording\n  const handleStartRecording = async () => {\n    console.log('handleStartRecording called');\n    if (!webcamRef.current || !webcamRef.current.stream) {\n      setError('Camera not available');\n      return;\n    }\n    try {\n      setRecordedChunks([]);\n      recordedChunksRef.current = [];\n      setRecordingTime(0);\n      setIsRecording(true);\n      setError('');\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\n        mimeType: 'video/webm'\n      });\n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.ondataavailable = event => {\n        console.log('ondataavailable fired, size:', event.data.size);\n        if (event.data.size > 0) {\n          setRecordedChunks(prev => {\n            const updated = [...prev, event.data];\n            recordedChunksRef.current = updated; // <-- Keep ref in sync\n            return updated;\n          });\n        }\n      };\n      mediaRecorder.onstop = () => {\n        console.log('mediaRecorder.onstop fired');\n        // Use the ref to get the latest chunks\n        const chunks = recordedChunksRef.current;\n        // Debug logs\n        console.log('onstop: recordedChunks length:', chunks.length);\n        let totalSize = 0;\n        chunks.forEach((c, i) => {\n          console.log(`Chunk ${i} size:`, c.size);\n          totalSize += c.size;\n        });\n        console.log('Total recorded size:', totalSize);\n        const blob = new Blob(chunks, {\n          type: 'video/webm'\n        });\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, {\n          type: 'video/webm'\n        });\n        setVideoFile(file);\n        setVideoPreview(URL.createObjectURL(blob));\n        setIsRecording(false);\n        setRecordingTime(0);\n        // Reset the ref and state for next recording\n        recordedChunksRef.current = [];\n        setRecordedChunks([]);\n      };\n      mediaRecorder.onstart = () => {\n        console.log('mediaRecorder.onstart fired');\n      };\n      mediaRecorder.onerror = e => {\n        console.error('mediaRecorder.onerror', e);\n      };\n      console.log('Calling mediaRecorder.start(1000)');\n      mediaRecorder.start(1000); // timeslice: 1000ms\n    } catch (error) {\n      setError('Failed to start recording: ' + error.message);\n      setIsRecording(false);\n    }\n  };\n\n  // Stop recording\n  const handleStopRecording = useCallback(() => {\n    console.log('handleStopRecording called');\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setRecordingTime(0);\n    }\n  }, [isRecording]);\n\n  // Toggle camera orientation\n  const toggleCameraOrientation = () => {\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\n  };\n\n  // Check if ready for processing\n  const isReadyForProcessing = () => {\n    return inputSource === 'video' && videoFile || inputSource === 'camera' && videoFile;\n  };\n\n  // Handle video processing\n  const handleProcess = async () => {\n    if (!isReadyForProcessing()) {\n      setError('Please provide a video file first');\n      return;\n    }\n\n    // Reset states\n    setLoading(true);\n    setError('');\n    setIsProcessing(true);\n    setShouldStop(false);\n    setIsBuffering(true);\n    setIsPlaying(false);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setProcessingProgress(0);\n    setAllDetections([]);\n    setCurrentDetections([]);\n    setProcessedVideo(null);\n    setVideoResults(null);\n    setShowResults(false);\n\n    // Create abort controller for cleanup\n    streamRef.current = new AbortController();\n    try {\n      const formData = new FormData();\n      formData.append('video', videoFile);\n      formData.append('selectedModel', selectedModel);\n      formData.append('coordinates', coordinates);\n      const userString = sessionStorage.getItem('user');\n      const user = userString ? JSON.parse(userString) : null;\n      formData.append('username', (user === null || user === void 0 ? void 0 : user.username) || 'Unknown');\n      formData.append('role', (user === null || user === void 0 ? void 0 : user.role) || 'Unknown');\n      console.log('Starting video processing with model:', selectedModel);\n      const sseUrl = '/api/pavement/detect-video';\n      const response = await fetch(sseUrl, {\n        method: 'POST',\n        body: formData,\n        signal: streamRef.current.signal\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      let buffer = '';\n\n      // Helper to accumulate detections\n      const appendDetections = detections => {\n        if (detections && Array.isArray(detections) && detections.length > 0) {\n          setAllDetections(prev => [...prev, ...detections]);\n        }\n      };\n      const processStream = async () => {\n        try {\n          while (true) {\n            const {\n              done,\n              value\n            } = await reader.read();\n            if (done) {\n              console.log('Stream ended naturally');\n              setIsProcessing(false);\n              setLoading(false);\n              setIsBuffering(false);\n              break;\n            }\n            buffer += decoder.decode(value, {\n              stream: true\n            });\n            let lines = buffer.split('\\n');\n            // Keep the last line in buffer if it's incomplete\n            buffer = lines.pop();\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                try {\n                  const data = JSON.parse(line.substring(6));\n                  // Debug log for every SSE message\n                  console.log('SSE data:', data);\n\n                  // Handle error case\n                  if (data.success === false) {\n                    setError(data.message || 'Video processing failed');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n\n                  // Update progress immediately\n                  if (data.progress !== undefined && totalFramesValid) {\n                    setProcessingProgress(data.progress);\n                    if (!showResults && data.progress > 0) {\n                      setShowResults(true);\n                    }\n                  } else if (data.frame_count !== undefined && totalFramesValid) {\n                    // Calculate progress if not provided but backend totalFrames is valid\n                    const progress = data.frame_count / totalFrames * 100;\n                    setProcessingProgress(progress);\n                    if (!showResults && progress > 0) {\n                      setShowResults(true);\n                    }\n                  } else if (data.frame_count !== undefined && estimatedTotalFrames) {\n                    // Fallback: use estimated total frames from duration and FPS\n                    const progress = data.frame_count / estimatedTotalFrames * 100;\n                    setProcessingProgress(progress);\n                    if (!showResults && progress > 0) {\n                      setShowResults(true);\n                    }\n                  }\n\n                  // Update frame display immediately\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\n                    setFrameBuffer(prev => {\n                      const newBuffer = [...prev, data.frame];\n                      return newBuffer;\n                    });\n                    setProcessedVideo(data.frame);\n                    setCurrentFrameIndex(prev => prev + 1);\n                    if (isBuffering) {\n                      setIsBuffering(false);\n                    }\n                  }\n\n                  // Accumulate detections per frame\n                  if (data.detections && data.detections.length > 0) {\n                    setCurrentDetections(data.detections);\n                    appendDetections(data.detections);\n                  }\n\n                  // Handle final results\n                  if (data.all_detections) {\n                    setVideoResults(data);\n                    setAllDetections(data.all_detections);\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    setProcessingProgress(100);\n                    setCurrentFrameIndex(0);\n                    setIsPlaying(false);\n                    console.log('Video processing completed');\n                    return;\n                  }\n\n                  // Handle end signal\n                  if (data.end) {\n                    console.log('Received end signal');\n                    setIsProcessing(false);\n                    setLoading(false);\n                    setIsBuffering(false);\n                    return;\n                  }\n\n                  // Update totalFrames when receiving SSE data:\n                  if (data.total_frames !== undefined) {\n                    setTotalFrames(data.total_frames);\n                  }\n                } catch (parseError) {\n                  console.warn('Error parsing SSE data:', parseError);\n                }\n              }\n            }\n          }\n        } catch (streamError) {\n          if (streamError.name === 'AbortError') {\n            console.log('Stream aborted by user');\n          } else {\n            console.error('Stream processing error:', streamError);\n            setError('Error processing video stream');\n          }\n          setIsProcessing(false);\n          setLoading(false);\n          setIsBuffering(false);\n        } finally {\n          if (reader) {\n            try {\n              reader.releaseLock();\n            } catch (e) {\n              console.warn('Error releasing reader lock:', e);\n            }\n          }\n        }\n      };\n      await processStream();\n    } catch (error) {\n      console.error('Video processing error:', error);\n      setError(error.message || 'Video processing failed');\n      setLoading(false);\n      setIsProcessing(false);\n    }\n  };\n\n  // Stop processing\n  const handleStopProcessing = async () => {\n    try {\n      await axios.post('/api/pavement/stop-video-processing');\n      setIsProcessing(false);\n      setShouldStop(true);\n      setIsBuffering(false);\n      setIsPlaying(false);\n      setLoading(false);\n      setError('Video processing stopped');\n    } catch (error) {\n      console.error('Error stopping processing:', error);\n      setError('Failed to stop processing');\n    }\n  };\n\n  // Reset all\n  const handleReset = () => {\n    setVideoFile(null);\n    setVideoPreview(null);\n    setProcessedVideo(null);\n    setVideoResults(null);\n    setAllDetections([]);\n    setCurrentDetections([]);\n    setFrameBuffer([]);\n    setCurrentFrameIndex(0);\n    setIsProcessing(false);\n    setShouldStop(false);\n    setIsBuffering(false);\n    setIsPlaying(false);\n    setProcessingProgress(0);\n    setError('');\n    setSelectedModel('All');\n    setShowResults(false); // <-- Ensure table is hidden after reset\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Playback controls (unused for now)\n  // const handlePlayPause = () => setIsPlaying(!isPlaying);\n  // const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\n  // const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\n\n  // Group detections by type\n  const getDetectionSummary = () => {\n    const summary = {};\n    allDetections.forEach(det => {\n      summary[det.type] = (summary[det.type] || 0) + 1;\n    });\n    return summary;\n  };\n\n  // Get tracking statistics\n  const getTrackingStats = () => {\n    if (videoResults) {\n      return {\n        uniqueDetections: videoResults.total_unique_detections || allDetections.length,\n        frameDetections: videoResults.total_frame_detections || allDetections.length,\n        duplicatesRemoved: (videoResults.total_frame_detections || allDetections.length) - (videoResults.total_unique_detections || allDetections.length)\n      };\n    }\n    return {\n      uniqueDetections: allDetections.length,\n      frameDetections: allDetections.length,\n      duplicatesRemoved: 0\n    };\n  };\n\n  // Format time\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // Location and routing handlers\n  const handleRouteReady = route => {\n    setPlannedRoute(route);\n    if (route) {\n      setLocationEnabled(true);\n      console.log('✅ Route ready for tracking:', route);\n    } else {\n      setLocationEnabled(false);\n    }\n  };\n  const handleTrackingStart = sessionId => {\n    setTrackingSession(sessionId);\n    console.log('🎯 Tracking started:', sessionId);\n  };\n  const handleTrackingStop = () => {\n    setTrackingSession(null);\n    console.log('🛑 Tracking stopped');\n  };\n  const handleDeviationAlert = alert => {\n    setDeviationAlerts(prev => [...prev, alert]);\n\n    // Show browser notification if permission granted\n    if (Notification.permission === 'granted') {\n      new Notification('Route Deviation Alert', {\n        body: alert.message,\n        icon: '/favicon.ico'\n      });\n    }\n    console.warn('⚠️ Deviation alert:', alert);\n  };\n\n  // Request notification permission on component mount\n  useEffect(() => {\n    if ('Notification' in window && Notification.permission === 'default') {\n      Notification.requestPermission();\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-defect-detection\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-primary text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"Video Defect Detection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this), locationEnabled && plannedRoute && /*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"success\",\n                children: \"Route Ready\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(Tabs, {\n              activeKey: activeTab,\n              onSelect: k => setActiveTab(k),\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Tab, {\n                eventKey: \"detection\",\n                title: \"Detection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                eventKey: \"route\",\n                title: \"Route Planning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                eventKey: \"tracking\",\n                title: \"Live Tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), activeTab === 'detection' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"danger\",\n                className: \"mb-3\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), warning && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"warning\",\n                className: \"mb-3\",\n                children: warning\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Detection Model\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: selectedModel,\n                  onChange: e => setSelectedModel(e.target.value),\n                  disabled: isProcessing,\n                  children: modelOptions.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option.value,\n                    children: option.label\n                  }, option.value, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Input Source\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: inputSource,\n                  onChange: e => setInputSource(e.target.value),\n                  disabled: isProcessing,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"video\",\n                    children: \"Video Upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"camera\",\n                    children: \"Live Camera Recording\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 15\n              }, this), inputSource === 'video' && /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Upload Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"file\",\n                  accept: \"video/*\",\n                  onChange: handleVideoChange,\n                  ref: fileInputRef,\n                  disabled: isProcessing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this), videoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"video\", {\n                    src: videoPreview,\n                    controls: true,\n                    className: \"video-preview\",\n                    style: {\n                      maxHeight: '200px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), inputSource === 'camera' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-2 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: cameraActive ? \"danger\" : \"info\",\n                    onClick: toggleCamera,\n                    disabled: isProcessing,\n                    children: cameraActive ? 'Stop Camera' : 'Start Camera'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 21\n                  }, this), isMobile && cameraActive && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    onClick: toggleCameraOrientation,\n                    size: \"sm\",\n                    children: \"Rotate Camera\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 19\n                }, this), cameraActive && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"webcam-container\",\n                  children: [/*#__PURE__*/_jsxDEV(Webcam, {\n                    audio: false,\n                    ref: webcamRef,\n                    screenshotFormat: \"image/jpeg\",\n                    width: \"100%\",\n                    height: \"auto\",\n                    videoConstraints: {\n                      width: 640,\n                      height: 480,\n                      facingMode: cameraOrientation\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2\",\n                    children: !isRecording ? /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      onClick: handleStartRecording,\n                      disabled: isProcessing,\n                      children: \"Start Recording\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 785,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"danger\",\n                        onClick: handleStopRecording,\n                        children: \"Stop Recording\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 794,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-danger\",\n                        children: [\"Recording: \", formatTime(recordingTime), \" / \", formatTime(MAX_RECORDING_TIME)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 800,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 21\n                }, this), videoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"video\", {\n                    src: videoPreview,\n                    controls: true,\n                    className: \"video-preview\",\n                    style: {\n                      maxHeight: '200px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"action-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"primary\",\n                  onClick: handleProcess,\n                  disabled: !isReadyForProcessing() || isProcessing,\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                      size: \"sm\",\n                      className: \"me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 23\n                    }, this), \"Processing...\"]\n                  }, void 0, true) : 'Process Video'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 17\n                }, this), isProcessing && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"warning\",\n                  onClick: handleStopProcessing,\n                  children: \"Stop Processing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"secondary\",\n                  onClick: handleReset,\n                  disabled: isProcessing,\n                  children: \"Reset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 15\n              }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-3\",\n                children: (totalFramesValid || estimatedTotalFrames) && processingProgress > 0 && Number.isFinite(processingProgress) ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex justify-content-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Processing Progress:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 863,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [Math.max(0, Math.min(100, processingProgress)).toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress-bar progress-bar-striped progress-bar-animated\",\n                      role: \"progressbar\",\n                      style: {\n                        width: `${Math.max(0, Math.min(100, processingProgress))}%`\n                      },\n                      \"aria-valuenow\": Math.max(0, Math.min(100, processingProgress)),\n                      \"aria-valuemin\": \"0\",\n                      \"aria-valuemax\": \"100\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Processing...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress flex-grow-1 ms-2\",\n                    style: {\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"progress-bar progress-bar-striped progress-bar-animated\",\n                      role: \"progressbar\",\n                      style: {\n                        width: `100%`,\n                        backgroundColor: '#e0e0e0'\n                      },\n                      \"aria-valuenow\": 0,\n                      \"aria-valuemin\": \"0\",\n                      \"aria-valuemax\": \"100\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 881,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 880,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this), activeTab === 'route' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(RoutePlanner, {\n                  onRouteReady: handleRouteReady,\n                  disabled: isProcessing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 17\n              }, this), activeTab === 'tracking' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: plannedRoute ? /*#__PURE__*/_jsxDEV(LiveTracking, {\n                  route: plannedRoute,\n                  onTrackingStart: handleTrackingStart,\n                  onTrackingStop: handleTrackingStop,\n                  onDeviationAlert: handleDeviationAlert,\n                  isActive: isRecording || isProcessing,\n                  disabled: !locationEnabled\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                  variant: \"info\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      children: \"No Route Planned\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Please go to the \\\"Route Planning\\\" tab to set up your pickup and drop locations first.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"primary\",\n                      onClick: () => setActiveTab('route'),\n                      children: \"Plan Route\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        children: (showResults || allDetections.length > 0 || !isProcessing && videoResults && allDetections.length > 0) && /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"bg-info text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: \"Detection Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 17\n            }, this), isProcessing && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-white-50\",\n              children: \"Results update in real-time as processing continues...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 19\n            }, this), !isProcessing && videoResults && /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-success\",\n              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                children: \"Processing Complete.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 21\n              }, this), \" Final results are shown below.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detection-summary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Detection Summary:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: Object.entries(getDetectionSummary()).map(([type, count]) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"badge bg-secondary me-1\",\n                  children: [type, \": \", count]\n                }, type, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tracking-stats\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Tracking Stats:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 23\n                  }, this), \" \", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-success me-1\",\n                    children: [\"Unique: \", getTrackingStats().uniqueDetections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-info me-1\",\n                    children: [\"Total Frames: \", getTrackingStats().frameDetections]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-warning\",\n                    children: [\"Duplicates Removed: \", getTrackingStats().duplicatesRemoved]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 979,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 17\n            }, this), (() => {\n              const potholeDetections = allDetections.filter(d => d.type === 'Pothole');\n              const crackDetections = allDetections.filter(d => d.type.includes('Crack'));\n              const kerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [(selectedModel === 'All' || selectedModel === 'Potholes') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section potholes mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-danger\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDD73\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 29\n                    }, this), \"Potholes Detected: \", potholeDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 27\n                  }, this), potholeDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1006,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1007,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Depth (cm)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1008,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume (cm\\xB3)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1009,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Volume Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1010,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1005,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1004,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: potholeDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1016,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1017,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1018,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.volume ? detection.volume.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1019,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.volume_range || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1020,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1015,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1013,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No potholes detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 25\n                }, this), (selectedModel === 'All' || selectedModel === 'Alligator Cracks') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section cracks mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-success\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83E\\uDEA8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1036,\n                      columnNumber: 29\n                    }, this), \"Cracks Detected: \", crackDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 27\n                  }, this), crackDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1044,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Type\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1045,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area (cm\\xB2)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1046,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Area Range\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1047,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1043,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1042,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: crackDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1053,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1054,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1055,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.area_range || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1056,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1052,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1050,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1041,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No cracks detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 25\n                }, this), (selectedModel === 'All' || selectedModel === 'Kerbs') && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"defect-section kerbs mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"text-primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"emoji\",\n                      children: \"\\uD83D\\uDEA7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1072,\n                      columnNumber: 29\n                    }, this), \"Kerbs Detected: \", kerbDetections.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 27\n                  }, this), kerbDetections.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"detection-table-container\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      striped: true,\n                      bordered: true,\n                      hover: true,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"ID\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1080,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Type\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1081,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Condition\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1082,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Length\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1083,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1079,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1078,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: kerbDetections.map((detection, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.track_id || index + 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1089,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.kerb_type || 'Concrete Kerb'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1090,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.condition || detection.type\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1091,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: detection.length_m ? detection.length_m.toFixed(2) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1092,\n                            columnNumber: 39\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1088,\n                          columnNumber: 37\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1086,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1077,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"no-defects-message\",\n                    children: \"No kerbs detected\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1099,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 21\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 647,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoDefectDetection, \"XdYHp4vtfPgpDHgDL08Tc+NaHe8=\", false, function () {\n  return [useResponsive];\n});\n_c = VideoDefectDetection;\nexport default VideoDefectDetection;\nvar _c;\n$RefreshReg$(_c, \"VideoDefectDetection\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Spinner", "Table", "Row", "Col", "Tab", "Tabs", "Badge", "axios", "Webcam", "useResponsive", "validateUploadFile", "showFileValidationError", "Route<PERSON><PERSON>ner", "LiveTracking", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoDefectDetection", "_s", "selected<PERSON><PERSON>l", "setSelectedModel", "videoFile", "setVideoFile", "videoPreview", "setVideoPreview", "processedVideo", "setProcessedVideo", "loading", "setLoading", "error", "setError", "isProcessing", "setIsProcessing", "shouldStop", "setShouldStop", "coordinates", "setCoordinates", "inputSource", "setInputSource", "cameraActive", "setCameraActive", "cameraOrientation", "setCameraOrientation", "isRecording", "setIsRecording", "recordingTime", "setRecordingTime", "recordedChunks", "setRecordedChunks", "recordedChunksRef", "activeTab", "setActiveTab", "plannedRoute", "setPlannedRoute", "trackingSession", "setTrackingSession", "deviationAlerts", "setDeviationAlerts", "locationEnabled", "setLocationEnabled", "frameBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentFrameIndex", "setCurrentFrameIndex", "isBuffering", "setIsBuffering", "isPlaying", "setIsPlaying", "processingProgress", "setProcessingProgress", "allDetections", "setAllDetections", "videoResults", "setVideoResults", "currentDetections", "setCurrentDetections", "showResults", "setShowResults", "streamRef", "webcamRef", "fileInputRef", "recordingTimerRef", "mediaRecorderRef", "isMobile", "PLAYBACK_FPS", "MAX_RECORDING_TIME", "totalFrames", "setTotalFrames", "totalFramesValid", "Number", "isFinite", "videoDuration", "setVideoDuration", "videoFPS", "setVideoFPS", "modelOptions", "value", "label", "navigator", "geolocation", "getCurrentPosition", "position", "latitude", "longitude", "coords", "toFixed", "err", "console", "current", "setInterval", "prev", "handleStopRecording", "clearInterval", "playbackInterval", "length", "video", "document", "createElement", "src", "preload", "onloadedmetadata", "duration", "webkitVideoDecodedByteCount", "undefined", "tracks", "videoTracks", "captureStream", "getVideoTracks", "getSettings", "settings", "frameRate", "e", "estimatedTotalFrames", "Math", "round", "abort", "warning", "setWarning", "handleVideoChange", "file", "target", "files", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "URL", "createObjectURL", "toggleCamera", "handleStartRecording", "log", "stream", "mediaRecorder", "MediaRecorder", "mimeType", "ondataavailable", "event", "data", "size", "updated", "onstop", "chunks", "totalSize", "for<PERSON>ach", "c", "i", "blob", "Blob", "type", "File", "Date", "now", "onstart", "onerror", "start", "message", "stop", "toggleCameraOrientation", "isReadyForProcessing", "handleProcess", "AbortController", "formData", "FormData", "append", "userString", "sessionStorage", "getItem", "user", "JSON", "parse", "username", "role", "sseUrl", "response", "fetch", "method", "body", "signal", "ok", "Error", "status", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "buffer", "appendDetections", "detections", "Array", "isArray", "processStream", "done", "read", "decode", "lines", "split", "pop", "line", "startsWith", "substring", "success", "progress", "frame_count", "frame", "new<PERSON>uffer", "all_detections", "end", "total_frames", "parseError", "warn", "streamError", "name", "releaseLock", "handleStopProcessing", "post", "handleReset", "getDetectionSummary", "summary", "det", "getTrackingStats", "uniqueDetections", "total_unique_detections", "frameDetections", "total_frame_detections", "duplicates<PERSON><PERSON>oved", "formatTime", "seconds", "mins", "floor", "secs", "toString", "padStart", "handleRouteReady", "route", "handleTrackingStart", "sessionId", "handleTrackingStop", "handleDeviationAlert", "alert", "Notification", "permission", "icon", "window", "requestPermission", "className", "children", "md", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bg", "Body", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "variant", "Group", "Label", "Select", "onChange", "disabled", "map", "option", "Control", "accept", "ref", "controls", "style", "maxHeight", "onClick", "audio", "screenshotFormat", "width", "height", "videoConstraints", "facingMode", "max", "min", "backgroundColor", "onRouteReady", "onTrackingStart", "onTrackingStop", "onDeviationAlert", "isActive", "Object", "entries", "count", "potholeDetections", "filter", "d", "crackDetections", "includes", "kerbDetections", "striped", "bordered", "hover", "detection", "index", "track_id", "area_cm2", "depth_cm", "volume", "volume_range", "area_range", "kerb_type", "condition", "length_m", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/VideoDefectDetection.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\r\nimport { Card, Button, Form, Alert, Spinner, Table, Row, Col, Tab, Tabs, Badge } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport Webcam from 'react-webcam';\r\nimport useResponsive from '../hooks/useResponsive';\r\nimport './VideoDefectDetection.css';\r\nimport { validateUploadFile, showFileValidationError } from '../utils/fileValidation';\r\nimport RoutePlanner from './RoutePlanner';\r\nimport LiveTracking from './LiveTracking';\r\n\r\nconst VideoDefectDetection = () => {\r\n  const [selectedModel, setSelectedModel] = useState('All');\r\n  const [videoFile, setVideoFile] = useState(null);\r\n  const [videoPreview, setVideoPreview] = useState(null);\r\n  const [processedVideo, setProcessedVideo] = useState(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [shouldStop, setShouldStop] = useState(false);\r\n  const [coordinates, setCoordinates] = useState('Not Available');\r\n  const [inputSource, setInputSource] = useState('video');\r\n  const [cameraActive, setCameraActive] = useState(false);\r\n  const [cameraOrientation, setCameraOrientation] = useState('environment');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [recordedChunks, setRecordedChunks] = useState([]);\r\n  const recordedChunksRef = useRef([]); // <-- Add this line\r\n\r\n  // Location and routing states\r\n  const [activeTab, setActiveTab] = useState('detection');\r\n  const [plannedRoute, setPlannedRoute] = useState(null);\r\n  const [trackingSession, setTrackingSession] = useState(null);\r\n  const [deviationAlerts, setDeviationAlerts] = useState([]);\r\n  const [locationEnabled, setLocationEnabled] = useState(false);\r\n  \r\n  // Video processing states\r\n  const [frameBuffer, setFrameBuffer] = useState([]);\r\n  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);\r\n  const [isBuffering, setIsBuffering] = useState(false);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [processingProgress, setProcessingProgress] = useState(0);\r\n  const [allDetections, setAllDetections] = useState([]);\r\n  const [videoResults, setVideoResults] = useState(null);\r\n  \r\n  // Add new state for frame-by-frame updates\r\n  const [currentDetections, setCurrentDetections] = useState([]);\r\n  const [showResults, setShowResults] = useState(false);\r\n  const streamRef = useRef(null);\r\n\r\n  const webcamRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const recordingTimerRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const { isMobile } = useResponsive();\r\n  \r\n  // const BUFFER_SIZE = 10; // Unused for now\r\n  const PLAYBACK_FPS = 15;\r\n  const MAX_RECORDING_TIME = 60; // 1 minute limit\r\n\r\n  const [totalFrames, setTotalFrames] = useState(null);\r\n  const totalFramesValid = Number.isFinite(totalFrames) && totalFrames > 0;\r\n\r\n  const [videoDuration, setVideoDuration] = useState(null);\r\n  const [videoFPS, setVideoFPS] = useState(30); // Default FPS\r\n\r\n  // Available models\r\n  const modelOptions = [\r\n    { value: 'All', label: 'All (detect all types of defects)' },\r\n    { value: 'Potholes', label: 'Potholes' },\r\n    { value: 'Alligator Cracks', label: 'Alligator Cracks' },\r\n    { value: 'Kerbs', label: 'Kerbs' }\r\n  ];\r\n\r\n  // Get user location\r\n  useEffect(() => {\r\n    if (navigator.geolocation) {\r\n      navigator.geolocation.getCurrentPosition(\r\n        (position) => {\r\n          const { latitude, longitude } = position.coords;\r\n          setCoordinates(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);\r\n        },\r\n        (err) => {\r\n          console.error(\"Error getting location:\", err);\r\n          setCoordinates('Location unavailable');\r\n        }\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  // Recording timer\r\n  useEffect(() => {\r\n    if (isRecording) {\r\n      recordingTimerRef.current = setInterval(() => {\r\n        setRecordingTime(prev => {\r\n          if (prev >= MAX_RECORDING_TIME) {\r\n            handleStopRecording();\r\n            return MAX_RECORDING_TIME;\r\n          }\r\n          return prev + 1;\r\n        });\r\n      }, 1000);\r\n    } else {\r\n      if (recordingTimerRef.current) {\r\n        clearInterval(recordingTimerRef.current);\r\n      }\r\n    }\r\n    \r\n    return () => {\r\n      if (recordingTimerRef.current) {\r\n        clearInterval(recordingTimerRef.current);\r\n      }\r\n    };\r\n  }, [isRecording, handleStopRecording, MAX_RECORDING_TIME]);\r\n\r\n  // Video playback effect\r\n  useEffect(() => {\r\n    let playbackInterval;\r\n    if (isPlaying && frameBuffer.length > 0) {\r\n      playbackInterval = setInterval(() => {\r\n        setCurrentFrameIndex(prev => {\r\n          if (prev < frameBuffer.length - 1) {\r\n            return prev + 1;\r\n          } else {\r\n            setIsPlaying(false);\r\n            return prev;\r\n          }\r\n        });\r\n      }, 1000 / PLAYBACK_FPS);\r\n    }\r\n    return () => {\r\n      if (playbackInterval) clearInterval(playbackInterval);\r\n    };\r\n  }, [isPlaying, frameBuffer]);\r\n\r\n  // Update processed video when frame changes\r\n  useEffect(() => {\r\n    if (frameBuffer.length > 0 && currentFrameIndex < frameBuffer.length) {\r\n      setProcessedVideo(frameBuffer[currentFrameIndex]);\r\n    }\r\n  }, [currentFrameIndex, frameBuffer]);\r\n\r\n  // When videoPreview is set, extract duration and FPS\r\n  useEffect(() => {\r\n    if (videoPreview) {\r\n      const video = document.createElement('video');\r\n      video.src = videoPreview;\r\n      video.preload = 'metadata';\r\n      video.onloadedmetadata = () => {\r\n        setVideoDuration(video.duration);\r\n        // Try to get FPS from video tracks if available\r\n        if (video.webkitVideoDecodedByteCount !== undefined) {\r\n          // Not standard, but some browsers may expose frameRate\r\n          try {\r\n            const tracks = video.videoTracks || (video.captureStream && video.captureStream().getVideoTracks());\r\n            if (tracks && tracks.length > 0 && tracks[0].getSettings) {\r\n              const settings = tracks[0].getSettings();\r\n              if (settings.frameRate) {\r\n                setVideoFPS(settings.frameRate);\r\n              }\r\n            }\r\n          } catch (e) {}\r\n        }\r\n      };\r\n    }\r\n  }, [videoPreview]);\r\n\r\n  // Helper to estimate total frames if backend total_frames is invalid\r\n  const estimatedTotalFrames = videoDuration && videoFPS ? Math.round(videoDuration * videoFPS) : null;\r\n\r\n  // Add cleanup effect for SSE stream\r\n  useEffect(() => {\r\n    return () => {\r\n      if (streamRef.current) {\r\n        streamRef.current.abort();\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const [warning, setWarning] = useState('');\r\n\r\n  // Handle video file selection\r\n  const handleVideoChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate video file first\r\n      const validation = validateUploadFile(file, 'video', 'video_defect_detection');\r\n      if (!validation.isValid) {\r\n        showFileValidationError(validation.errorMessage, setError);\r\n        // Clear the file input\r\n        if (e.target) {\r\n          e.target.value = '';\r\n        }\r\n        return;\r\n      }\r\n\r\n      // Clear any previous errors\r\n      setError('');\r\n\r\n      const video = document.createElement('video');\r\n      video.preload = 'metadata';\r\n      video.onloadedmetadata = () => {\r\n        if (video.duration > 60) {\r\n          setWarning('Video upload is restricted to 1 minute. Please select a shorter video.');\r\n          setVideoFile(null);\r\n          setVideoPreview(null);\r\n          setProcessedVideo(null);\r\n          setVideoResults(null);\r\n          setAllDetections([]);\r\n          setError('');\r\n          if (fileInputRef.current) fileInputRef.current.value = '';\r\n        } else {\r\n          setWarning('');\r\n          setVideoFile(file);\r\n          setVideoPreview(URL.createObjectURL(file));\r\n          setProcessedVideo(null);\r\n          setVideoResults(null);\r\n          setAllDetections([]);\r\n          setError('');\r\n        }\r\n      };\r\n      video.src = URL.createObjectURL(file);\r\n    }\r\n  };\r\n\r\n  // Handle camera activation\r\n  const toggleCamera = () => {\r\n    setCameraActive(!cameraActive);\r\n    if (!cameraActive) {\r\n      setVideoFile(null);\r\n      setVideoPreview(null);\r\n      setProcessedVideo(null);\r\n      setVideoResults(null);\r\n      setAllDetections([]);\r\n      setError('');\r\n    }\r\n  };\r\n\r\n  // Start recording\r\n  const handleStartRecording = async () => {\r\n    console.log('handleStartRecording called');\r\n    if (!webcamRef.current || !webcamRef.current.stream) {\r\n      setError('Camera not available');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setRecordedChunks([]);\r\n      recordedChunksRef.current = [];\r\n      setRecordingTime(0);\r\n      setIsRecording(true);\r\n      setError('');\r\n\r\n      const mediaRecorder = new MediaRecorder(webcamRef.current.stream, {\r\n        mimeType: 'video/webm'\r\n      });\r\n\r\n      mediaRecorderRef.current = mediaRecorder;\r\n\r\n      mediaRecorder.ondataavailable = (event) => {\r\n        console.log('ondataavailable fired, size:', event.data.size);\r\n        if (event.data.size > 0) {\r\n          setRecordedChunks(prev => {\r\n            const updated = [...prev, event.data];\r\n            recordedChunksRef.current = updated; // <-- Keep ref in sync\r\n            return updated;\r\n          });\r\n        }\r\n      };\r\n\r\n      mediaRecorder.onstop = () => {\r\n        console.log('mediaRecorder.onstop fired');\r\n        // Use the ref to get the latest chunks\r\n        const chunks = recordedChunksRef.current;\r\n        // Debug logs\r\n        console.log('onstop: recordedChunks length:', chunks.length);\r\n        let totalSize = 0;\r\n        chunks.forEach((c, i) => {\r\n          console.log(`Chunk ${i} size:`, c.size);\r\n          totalSize += c.size;\r\n        });\r\n        console.log('Total recorded size:', totalSize);\r\n        const blob = new Blob(chunks, { type: 'video/webm' });\r\n        const file = new File([blob], `recorded_video_${Date.now()}.webm`, { type: 'video/webm' });\r\n        setVideoFile(file);\r\n        setVideoPreview(URL.createObjectURL(blob));\r\n        setIsRecording(false);\r\n        setRecordingTime(0);\r\n        // Reset the ref and state for next recording\r\n        recordedChunksRef.current = [];\r\n        setRecordedChunks([]);\r\n      };\r\n\r\n      mediaRecorder.onstart = () => {\r\n        console.log('mediaRecorder.onstart fired');\r\n      };\r\n      mediaRecorder.onerror = (e) => {\r\n        console.error('mediaRecorder.onerror', e);\r\n      };\r\n\r\n      console.log('Calling mediaRecorder.start(1000)');\r\n      mediaRecorder.start(1000); // timeslice: 1000ms\r\n    } catch (error) {\r\n      setError('Failed to start recording: ' + error.message);\r\n      setIsRecording(false);\r\n    }\r\n  };\r\n\r\n  // Stop recording\r\n  const handleStopRecording = useCallback(() => {\r\n    console.log('handleStopRecording called');\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setRecordingTime(0);\r\n    }\r\n  }, [isRecording]);\r\n\r\n  // Toggle camera orientation\r\n  const toggleCameraOrientation = () => {\r\n    setCameraOrientation(prev => prev === 'environment' ? 'user' : 'environment');\r\n  };\r\n\r\n  // Check if ready for processing\r\n  const isReadyForProcessing = () => {\r\n    return (inputSource === 'video' && videoFile) || \r\n           (inputSource === 'camera' && videoFile);\r\n  };\r\n\r\n  // Handle video processing\r\n  const handleProcess = async () => {\r\n    if (!isReadyForProcessing()) {\r\n      setError('Please provide a video file first');\r\n      return;\r\n    }\r\n\r\n    // Reset states\r\n    setLoading(true);\r\n    setError('');\r\n    setIsProcessing(true);\r\n    setShouldStop(false);\r\n    setIsBuffering(true);\r\n    setIsPlaying(false);\r\n    setFrameBuffer([]);\r\n    setCurrentFrameIndex(0);\r\n    setProcessingProgress(0);\r\n    setAllDetections([]);\r\n    setCurrentDetections([]);\r\n    setProcessedVideo(null);\r\n    setVideoResults(null);\r\n    setShowResults(false);\r\n\r\n    // Create abort controller for cleanup\r\n    streamRef.current = new AbortController();\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('video', videoFile);\r\n      formData.append('selectedModel', selectedModel);\r\n      formData.append('coordinates', coordinates);\r\n      const userString = sessionStorage.getItem('user');\r\n      const user = userString ? JSON.parse(userString) : null;\r\n      formData.append('username', user?.username || 'Unknown');\r\n      formData.append('role', user?.role || 'Unknown');\r\n\r\n      console.log('Starting video processing with model:', selectedModel);\r\n\r\n      const sseUrl = '/api/pavement/detect-video';\r\n      \r\n      const response = await fetch(sseUrl, {\r\n        method: 'POST',\r\n        body: formData,\r\n        signal: streamRef.current.signal\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder();\r\n      let buffer = '';\r\n\r\n      // Helper to accumulate detections\r\n      const appendDetections = (detections) => {\r\n        if (detections && Array.isArray(detections) && detections.length > 0) {\r\n          setAllDetections(prev => [...prev, ...detections]);\r\n        }\r\n      };\r\n\r\n      const processStream = async () => {\r\n        try {\r\n          while (true) {\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              console.log('Stream ended naturally');\r\n              setIsProcessing(false);\r\n              setLoading(false);\r\n              setIsBuffering(false);\r\n              break;\r\n            }\r\n\r\n            buffer += decoder.decode(value, { stream: true });\r\n            let lines = buffer.split('\\n');\r\n            // Keep the last line in buffer if it's incomplete\r\n            buffer = lines.pop();\r\n\r\n            for (const line of lines) {\r\n              if (line.startsWith('data: ')) {\r\n                try {\r\n                  const data = JSON.parse(line.substring(6));\r\n                  // Debug log for every SSE message\r\n                  console.log('SSE data:', data);\r\n\r\n                  // Handle error case\r\n                  if (data.success === false) {\r\n                    setError(data.message || 'Video processing failed');\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    return;\r\n                  }\r\n\r\n                  // Update progress immediately\r\n                  if (data.progress !== undefined && totalFramesValid) {\r\n                    setProcessingProgress(data.progress);\r\n                    if (!showResults && data.progress > 0) {\r\n                      setShowResults(true);\r\n                    }\r\n                  } else if (data.frame_count !== undefined && totalFramesValid) {\r\n                    // Calculate progress if not provided but backend totalFrames is valid\r\n                    const progress = (data.frame_count / totalFrames) * 100;\r\n                    setProcessingProgress(progress);\r\n                    if (!showResults && progress > 0) {\r\n                      setShowResults(true);\r\n                    }\r\n                  } else if (data.frame_count !== undefined && estimatedTotalFrames) {\r\n                    // Fallback: use estimated total frames from duration and FPS\r\n                    const progress = (data.frame_count / estimatedTotalFrames) * 100;\r\n                    setProcessingProgress(progress);\r\n                    if (!showResults && progress > 0) {\r\n                      setShowResults(true);\r\n                    }\r\n                  }\r\n\r\n                  // Update frame display immediately\r\n                  if (data.frame && typeof data.frame === 'string' && data.frame.length > 1000) {\r\n                    setFrameBuffer(prev => {\r\n                      const newBuffer = [...prev, data.frame];\r\n                      return newBuffer;\r\n                    });\r\n                    setProcessedVideo(data.frame);\r\n                    setCurrentFrameIndex(prev => prev + 1);\r\n                    if (isBuffering) {\r\n                      setIsBuffering(false);\r\n                    }\r\n                  }\r\n\r\n                  // Accumulate detections per frame\r\n                  if (data.detections && data.detections.length > 0) {\r\n                    setCurrentDetections(data.detections);\r\n                    appendDetections(data.detections);\r\n                  }\r\n\r\n                  // Handle final results\r\n                  if (data.all_detections) {\r\n                    setVideoResults(data);\r\n                    setAllDetections(data.all_detections);\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    setProcessingProgress(100);\r\n                    setCurrentFrameIndex(0);\r\n                    setIsPlaying(false);\r\n                    console.log('Video processing completed');\r\n                    return;\r\n                  }\r\n\r\n                  // Handle end signal\r\n                  if (data.end) {\r\n                    console.log('Received end signal');\r\n                    setIsProcessing(false);\r\n                    setLoading(false);\r\n                    setIsBuffering(false);\r\n                    return;\r\n                  }\r\n\r\n                  // Update totalFrames when receiving SSE data:\r\n                  if (data.total_frames !== undefined) {\r\n                    setTotalFrames(data.total_frames);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn('Error parsing SSE data:', parseError);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } catch (streamError) {\r\n          if (streamError.name === 'AbortError') {\r\n            console.log('Stream aborted by user');\r\n          } else {\r\n            console.error('Stream processing error:', streamError);\r\n            setError('Error processing video stream');\r\n          }\r\n          setIsProcessing(false);\r\n          setLoading(false);\r\n          setIsBuffering(false);\r\n        } finally {\r\n          if (reader) {\r\n            try {\r\n              reader.releaseLock();\r\n            } catch (e) {\r\n              console.warn('Error releasing reader lock:', e);\r\n            }\r\n          }\r\n        }\r\n      };\r\n\r\n      await processStream();\r\n    } catch (error) {\r\n      console.error('Video processing error:', error);\r\n      setError(error.message || 'Video processing failed');\r\n      setLoading(false);\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Stop processing\r\n  const handleStopProcessing = async () => {\r\n    try {\r\n      await axios.post('/api/pavement/stop-video-processing');\r\n      \r\n      setIsProcessing(false);\r\n      setShouldStop(true);\r\n      setIsBuffering(false);\r\n      setIsPlaying(false);\r\n      setLoading(false);\r\n      setError('Video processing stopped');\r\n    } catch (error) {\r\n      console.error('Error stopping processing:', error);\r\n      setError('Failed to stop processing');\r\n    }\r\n  };\r\n\r\n  // Reset all\r\n  const handleReset = () => {\r\n    setVideoFile(null);\r\n    setVideoPreview(null);\r\n    setProcessedVideo(null);\r\n    setVideoResults(null);\r\n    setAllDetections([]);\r\n    setCurrentDetections([]);\r\n    setFrameBuffer([]);\r\n    setCurrentFrameIndex(0);\r\n    setIsProcessing(false);\r\n    setShouldStop(false);\r\n    setIsBuffering(false);\r\n    setIsPlaying(false);\r\n    setProcessingProgress(0);\r\n    setError('');\r\n    setSelectedModel('All');\r\n    setShowResults(false); // <-- Ensure table is hidden after reset\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Playback controls (unused for now)\r\n  // const handlePlayPause = () => setIsPlaying(!isPlaying);\r\n  // const handleRewind = () => setCurrentFrameIndex(Math.max(currentFrameIndex - 5, 0));\r\n  // const handleForward = () => setCurrentFrameIndex(Math.min(currentFrameIndex + 5, frameBuffer.length - 1));\r\n\r\n  // Group detections by type\r\n  const getDetectionSummary = () => {\r\n    const summary = {};\r\n    allDetections.forEach(det => {\r\n      summary[det.type] = (summary[det.type] || 0) + 1;\r\n    });\r\n    return summary;\r\n  };\r\n\r\n  // Get tracking statistics\r\n  const getTrackingStats = () => {\r\n    if (videoResults) {\r\n      return {\r\n        uniqueDetections: videoResults.total_unique_detections || allDetections.length,\r\n        frameDetections: videoResults.total_frame_detections || allDetections.length,\r\n        duplicatesRemoved: (videoResults.total_frame_detections || allDetections.length) - (videoResults.total_unique_detections || allDetections.length)\r\n      };\r\n    }\r\n    return {\r\n      uniqueDetections: allDetections.length,\r\n      frameDetections: allDetections.length,\r\n      duplicatesRemoved: 0\r\n    };\r\n  };\r\n\r\n  // Format time\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  // Location and routing handlers\r\n  const handleRouteReady = (route) => {\r\n    setPlannedRoute(route);\r\n    if (route) {\r\n      setLocationEnabled(true);\r\n      console.log('✅ Route ready for tracking:', route);\r\n    } else {\r\n      setLocationEnabled(false);\r\n    }\r\n  };\r\n\r\n  const handleTrackingStart = (sessionId) => {\r\n    setTrackingSession(sessionId);\r\n    console.log('🎯 Tracking started:', sessionId);\r\n  };\r\n\r\n  const handleTrackingStop = () => {\r\n    setTrackingSession(null);\r\n    console.log('🛑 Tracking stopped');\r\n  };\r\n\r\n  const handleDeviationAlert = (alert) => {\r\n    setDeviationAlerts(prev => [...prev, alert]);\r\n\r\n    // Show browser notification if permission granted\r\n    if (Notification.permission === 'granted') {\r\n      new Notification('Route Deviation Alert', {\r\n        body: alert.message,\r\n        icon: '/favicon.ico'\r\n      });\r\n    }\r\n\r\n    console.warn('⚠️ Deviation alert:', alert);\r\n  };\r\n\r\n  // Request notification permission on component mount\r\n  useEffect(() => {\r\n    if ('Notification' in window && Notification.permission === 'default') {\r\n      Notification.requestPermission();\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"video-defect-detection\">\r\n      <Row>\r\n        <Col md={6}>\r\n          <Card className=\"mb-4\">\r\n            <Card.Header className=\"bg-primary text-white\">\r\n              <div className=\"d-flex align-items-center justify-content-between\">\r\n                <h5 className=\"mb-0\">Video Defect Detection</h5>\r\n                {locationEnabled && plannedRoute && (\r\n                  <Badge bg=\"success\">\r\n                    Route Ready\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            </Card.Header>\r\n            <Card.Body>\r\n              {/* Tab Navigation */}\r\n              <Tabs\r\n                activeKey={activeTab}\r\n                onSelect={(k) => setActiveTab(k)}\r\n                className=\"mb-3\"\r\n              >\r\n                <Tab eventKey=\"detection\" title=\"Detection\">\r\n                  {/* Detection content will go here */}\r\n                </Tab>\r\n                <Tab eventKey=\"route\" title=\"Route Planning\">\r\n                  {/* Route planning content will go here */}\r\n                </Tab>\r\n                <Tab eventKey=\"tracking\" title=\"Live Tracking\">\r\n                  {/* Live tracking content will go here */}\r\n                </Tab>\r\n              </Tabs>\r\n\r\n              {/* Tab Content */}\r\n              {activeTab === 'detection' && (\r\n                <div>\r\n              {error && (\r\n                <Alert variant=\"danger\" className=\"mb-3\">\r\n                  {error}\r\n                </Alert>\r\n              )}\r\n              {warning && (\r\n                <Alert variant=\"warning\" className=\"mb-3\">\r\n                  {warning}\r\n                </Alert>\r\n              )}\r\n\r\n              {/* Model Selection */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Detection Model</Form.Label>\r\n                <Form.Select\r\n                  value={selectedModel}\r\n                  onChange={(e) => setSelectedModel(e.target.value)}\r\n                  disabled={isProcessing}\r\n                >\r\n                  {modelOptions.map(option => (\r\n                    <option key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </option>\r\n                  ))}\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Input Source Selection */}\r\n              <Form.Group className=\"mb-3\">\r\n                <Form.Label>Input Source</Form.Label>\r\n                <Form.Select\r\n                  value={inputSource}\r\n                  onChange={(e) => setInputSource(e.target.value)}\r\n                  disabled={isProcessing}\r\n                >\r\n                  <option value=\"video\">Video Upload</option>\r\n                  <option value=\"camera\">Live Camera Recording</option>\r\n                </Form.Select>\r\n              </Form.Group>\r\n\r\n              {/* Video Upload */}\r\n              {inputSource === 'video' && (\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Upload Video</Form.Label>\r\n                  <Form.Control\r\n                    type=\"file\"\r\n                    accept=\"video/*\"\r\n                    onChange={handleVideoChange}\r\n                    ref={fileInputRef}\r\n                    disabled={isProcessing}\r\n                  />\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video\r\n                        src={videoPreview}\r\n                        controls\r\n                        className=\"video-preview\"\r\n                        style={{ maxHeight: '200px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </Form.Group>\r\n              )}\r\n\r\n              {/* Camera Recording */}\r\n              {inputSource === 'camera' && (\r\n                <div className=\"mb-3\">\r\n                  <div className=\"d-flex gap-2 mb-2\">\r\n                    <Button\r\n                      variant={cameraActive ? \"danger\" : \"info\"}\r\n                      onClick={toggleCamera}\r\n                      disabled={isProcessing}\r\n                    >\r\n                      {cameraActive ? 'Stop Camera' : 'Start Camera'}\r\n                    </Button>\r\n                    {isMobile && cameraActive && (\r\n                      <Button\r\n                        variant=\"outline-secondary\"\r\n                        onClick={toggleCameraOrientation}\r\n                        size=\"sm\"\r\n                      >\r\n                        Rotate Camera\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {cameraActive && (\r\n                    <div className=\"webcam-container\">\r\n                      <Webcam\r\n                        audio={false}\r\n                        ref={webcamRef}\r\n                        screenshotFormat=\"image/jpeg\"\r\n                        width=\"100%\"\r\n                        height=\"auto\"\r\n                        videoConstraints={{\r\n                          width: 640,\r\n                          height: 480,\r\n                          facingMode: cameraOrientation\r\n                        }}\r\n                      />\r\n                      \r\n                      <div className=\"mt-2\">\r\n                        {!isRecording ? (\r\n                          <Button\r\n                            variant=\"success\"\r\n                            onClick={handleStartRecording}\r\n                            disabled={isProcessing}\r\n                          >\r\n                            Start Recording\r\n                          </Button>\r\n                        ) : (\r\n                          <div className=\"d-flex align-items-center gap-2\">\r\n                            <Button\r\n                              variant=\"danger\"\r\n                              onClick={handleStopRecording}\r\n                            >\r\n                              Stop Recording\r\n                            </Button>\r\n                            <span className=\"text-danger\">\r\n                              Recording: {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}\r\n                            </span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {videoPreview && (\r\n                    <div className=\"mt-3\">\r\n                      <video\r\n                        src={videoPreview}\r\n                        controls\r\n                        className=\"video-preview\"\r\n                        style={{ maxHeight: '200px' }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"action-buttons\">\r\n                <Button\r\n                  variant=\"primary\"\r\n                  onClick={handleProcess}\r\n                  disabled={!isReadyForProcessing() || isProcessing}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <Spinner size=\"sm\" className=\"me-2\" />\r\n                      Processing...\r\n                    </>\r\n                  ) : (\r\n                    'Process Video'\r\n                  )}\r\n                </Button>\r\n                \r\n                {isProcessing && (\r\n                  <Button\r\n                    variant=\"warning\"\r\n                    onClick={handleStopProcessing}\r\n                  >\r\n                    Stop Processing\r\n                  </Button>\r\n                )}\r\n                \r\n                <Button\r\n                  variant=\"secondary\"\r\n                  onClick={handleReset}\r\n                  disabled={isProcessing}\r\n                >\r\n                  Reset\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Always show progress during processing */}\r\n              {isProcessing && (\r\n                <div className=\"mt-3\">\r\n                  {(totalFramesValid || estimatedTotalFrames) && processingProgress > 0 && Number.isFinite(processingProgress) ? (\r\n                    <>\r\n                      <div className=\"d-flex justify-content-between\">\r\n                        <span>Processing Progress:</span>\r\n                        <span>{Math.max(0, Math.min(100, processingProgress)).toFixed(1)}%</span>\r\n                      </div>\r\n                      <div className=\"progress mt-1\">\r\n                        <div\r\n                          className=\"progress-bar progress-bar-striped progress-bar-animated\"\r\n                          role=\"progressbar\"\r\n                          style={{ width: `${Math.max(0, Math.min(100, processingProgress))}%` }}\r\n                          aria-valuenow={Math.max(0, Math.min(100, processingProgress))}\r\n                          aria-valuemin=\"0\"\r\n                          aria-valuemax=\"100\"\r\n                        ></div>\r\n                      </div>\r\n                    </>\r\n                  ) : (\r\n                    <div className=\"d-flex align-items-center mt-3\">\r\n                      <span>Processing...</span>\r\n                      <div className=\"progress flex-grow-1 ms-2\" style={{ height: '20px' }}>\r\n                        <div\r\n                          className=\"progress-bar progress-bar-striped progress-bar-animated\"\r\n                          role=\"progressbar\"\r\n                          style={{ width: `100%`, backgroundColor: '#e0e0e0' }}\r\n                          aria-valuenow={0}\r\n                          aria-valuemin=\"0\"\r\n                          aria-valuemax=\"100\"\r\n                        ></div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Route Planning Tab */}\r\n              {activeTab === 'route' && (\r\n                <div>\r\n                  <RoutePlanner\r\n                    onRouteReady={handleRouteReady}\r\n                    disabled={isProcessing}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              {/* Live Tracking Tab */}\r\n              {activeTab === 'tracking' && (\r\n                <div>\r\n                  {plannedRoute ? (\r\n                    <LiveTracking\r\n                      route={plannedRoute}\r\n                      onTrackingStart={handleTrackingStart}\r\n                      onTrackingStop={handleTrackingStop}\r\n                      onDeviationAlert={handleDeviationAlert}\r\n                      isActive={isRecording || isProcessing}\r\n                      disabled={!locationEnabled}\r\n                    />\r\n                  ) : (\r\n                    <Alert variant=\"info\">\r\n                      <div className=\"text-center\">\r\n                        <h6>No Route Planned</h6>\r\n                        <p>Please go to the \"Route Planning\" tab to set up your pickup and drop locations first.</p>\r\n                        <Button\r\n                          variant=\"primary\"\r\n                          onClick={() => setActiveTab('route')}\r\n                        >\r\n                          Plan Route\r\n                        </Button>\r\n                      </div>\r\n                    </Alert>\r\n                  )}\r\n                </div>\r\n              )}\r\n              {/* Close detection tab content div */}\r\n              </div>\r\n              )}\r\n            </Card.Body>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col md={6}>\r\n          {/* Show detection results as soon as we have any, and always after processing is complete if results exist */}\r\n          {((showResults || allDetections.length > 0 || (!isProcessing && videoResults && allDetections.length > 0))) && (\r\n            <Card>\r\n              <Card.Header className=\"bg-info text-white\">\r\n                <h5 className=\"mb-0\">Detection Results</h5>\r\n                {isProcessing && (\r\n                  <small className=\"text-white-50\">\r\n                    Results update in real-time as processing continues...\r\n                  </small>\r\n                )}\r\n                {!isProcessing && videoResults && (\r\n                  <small className=\"text-success\">\r\n                    <b>Processing Complete.</b> Final results are shown below.\r\n                  </small>\r\n                )}\r\n              </Card.Header>\r\n              <Card.Body>\r\n                {/* Summary */}\r\n                <div className=\"detection-summary mb-3\">\r\n                  <h6>Detection Summary:</h6>\r\n                  <div className=\"mb-2\">\r\n                    {Object.entries(getDetectionSummary()).map(([type, count]) => (\r\n                      <span key={type} className=\"badge bg-secondary me-1\">\r\n                        {type}: {count}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                  \r\n                  {/* Tracking Statistics */}\r\n                  <div className=\"tracking-stats\">\r\n                    <small className=\"text-muted\">\r\n                      <strong>Tracking Stats:</strong> {' '}\r\n                      <span className=\"badge bg-success me-1\">\r\n                        Unique: {getTrackingStats().uniqueDetections}\r\n                      </span>\r\n                      <span className=\"badge bg-info me-1\">\r\n                        Total Frames: {getTrackingStats().frameDetections}\r\n                      </span>\r\n                      <span className=\"badge bg-warning\">\r\n                        Duplicates Removed: {getTrackingStats().duplicatesRemoved}\r\n                      </span>\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Separate Tables for Each Defect Type */}\r\n                {(() => {\r\n                  const potholeDetections = allDetections.filter(d => d.type === 'Pothole');\r\n                  const crackDetections = allDetections.filter(d => d.type.includes('Crack'));\r\n                  const kerbDetections = allDetections.filter(d => d.type.includes('Kerb'));\r\n\r\n                  return (\r\n                    <div>\r\n                      {/* Pothole Table - Show only if \"All\" or \"Potholes\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Potholes') && (\r\n                        <div className=\"defect-section potholes mb-4\">\r\n                          <h6 className=\"text-danger\">\r\n                            <span className=\"emoji\">🕳️</span>\r\n                            Potholes Detected: {potholeDetections.length}\r\n                          </h6>\r\n                          {potholeDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Depth (cm)</th>\r\n                                    <th>Volume (cm³)</th>\r\n                                    <th>Volume Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {potholeDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.depth_cm ? detection.depth_cm.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.volume ? detection.volume.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.volume_range || 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No potholes detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Cracks Table - Show only if \"All\" or \"Alligator Cracks\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Alligator Cracks') && (\r\n                        <div className=\"defect-section cracks mb-4\">\r\n                          <h6 className=\"text-success\">\r\n                            <span className=\"emoji\">🪨</span>\r\n                            Cracks Detected: {crackDetections.length}\r\n                          </h6>\r\n                          {crackDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Type</th>\r\n                                    <th>Area (cm²)</th>\r\n                                    <th>Area Range</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {crackDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.type}</td>\r\n                                      <td>{detection.area_cm2 ? detection.area_cm2.toFixed(2) : 'N/A'}</td>\r\n                                      <td>{detection.area_range || 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No cracks detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Kerbs Table - Show only if \"All\" or \"Kerbs\" is selected */}\r\n                      {(selectedModel === 'All' || selectedModel === 'Kerbs') && (\r\n                        <div className=\"defect-section kerbs mb-4\">\r\n                          <h6 className=\"text-primary\">\r\n                            <span className=\"emoji\">🚧</span>\r\n                            Kerbs Detected: {kerbDetections.length}\r\n                          </h6>\r\n                          {kerbDetections.length > 0 ? (\r\n                            <div className=\"detection-table-container\">\r\n                              <Table striped bordered hover size=\"sm\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>ID</th>\r\n                                    <th>Type</th>\r\n                                    <th>Condition</th>\r\n                                    <th>Length</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {kerbDetections.map((detection, index) => (\r\n                                    <tr key={index}>\r\n                                      <td>{detection.track_id || index + 1}</td>\r\n                                      <td>{detection.kerb_type || 'Concrete Kerb'}</td>\r\n                                      <td>{detection.condition || detection.type}</td>\r\n                                      <td>{detection.length_m ? detection.length_m.toFixed(2) : 'N/A'}</td>\r\n                                    </tr>\r\n                                  ))}\r\n                                </tbody>\r\n                              </Table>\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"no-defects-message\">No kerbs detected</div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })()}\r\n              </Card.Body>\r\n            </Card>\r\n          )}\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VideoDefectDetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACvG,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAO,4BAA4B;AACnC,SAASC,kBAAkB,EAAEC,uBAAuB,QAAQ,yBAAyB;AACrF,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,eAAe,CAAC;EAC/D,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnD,QAAQ,CAAC,aAAa,CAAC;EACzE,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM0D,iBAAiB,GAAGzD,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEtC;EACA,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiE,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACqE,WAAW,EAAEC,cAAc,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiF,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAACmF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMuF,SAAS,GAAGtF,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMuF,SAAS,GAAGvF,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMwF,YAAY,GAAGxF,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMyF,iBAAiB,GAAGzF,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM0F,gBAAgB,GAAG1F,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM;IAAE2F;EAAS,CAAC,GAAG3E,aAAa,CAAC,CAAC;;EAEpC;EACA,MAAM4E,YAAY,GAAG,EAAE;EACvB,MAAMC,kBAAkB,GAAG,EAAE,CAAC,CAAC;;EAE/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMiG,gBAAgB,GAAGC,MAAM,CAACC,QAAQ,CAACJ,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC;EAExE,MAAM,CAACK,aAAa,EAAEC,gBAAgB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsG,QAAQ,EAAEC,WAAW,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAMwG,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAoC,CAAC,EAC5D;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAmB,CAAC,EACxD;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACnC;;EAED;EACAxG,SAAS,CAAC,MAAM;IACd,IAAIyG,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZ,MAAM;UAAEC,QAAQ;UAAEC;QAAU,CAAC,GAAGF,QAAQ,CAACG,MAAM;QAC/CpE,cAAc,CAAC,GAAGkE,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,KAAKF,SAAS,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;MACnE,CAAC,EACAC,GAAG,IAAK;QACPC,OAAO,CAAC9E,KAAK,CAAC,yBAAyB,EAAE6E,GAAG,CAAC;QAC7CtE,cAAc,CAAC,sBAAsB,CAAC;MACxC,CACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3C,SAAS,CAAC,MAAM;IACd,IAAIkD,WAAW,EAAE;MACfsC,iBAAiB,CAAC2B,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC5C/D,gBAAgB,CAACgE,IAAI,IAAI;UACvB,IAAIA,IAAI,IAAIzB,kBAAkB,EAAE;YAC9B0B,mBAAmB,CAAC,CAAC;YACrB,OAAO1B,kBAAkB;UAC3B;UACA,OAAOyB,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAI7B,iBAAiB,CAAC2B,OAAO,EAAE;QAC7BI,aAAa,CAAC/B,iBAAiB,CAAC2B,OAAO,CAAC;MAC1C;IACF;IAEA,OAAO,MAAM;MACX,IAAI3B,iBAAiB,CAAC2B,OAAO,EAAE;QAC7BI,aAAa,CAAC/B,iBAAiB,CAAC2B,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,CAACjE,WAAW,EAAEoE,mBAAmB,EAAE1B,kBAAkB,CAAC,CAAC;;EAE1D;EACA5F,SAAS,CAAC,MAAM;IACd,IAAIwH,gBAAgB;IACpB,IAAI/C,SAAS,IAAIN,WAAW,CAACsD,MAAM,GAAG,CAAC,EAAE;MACvCD,gBAAgB,GAAGJ,WAAW,CAAC,MAAM;QACnC9C,oBAAoB,CAAC+C,IAAI,IAAI;UAC3B,IAAIA,IAAI,GAAGlD,WAAW,CAACsD,MAAM,GAAG,CAAC,EAAE;YACjC,OAAOJ,IAAI,GAAG,CAAC;UACjB,CAAC,MAAM;YACL3C,YAAY,CAAC,KAAK,CAAC;YACnB,OAAO2C,IAAI;UACb;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,GAAG1B,YAAY,CAAC;IACzB;IACA,OAAO,MAAM;MACX,IAAI6B,gBAAgB,EAAED,aAAa,CAACC,gBAAgB,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAAC/C,SAAS,EAAEN,WAAW,CAAC,CAAC;;EAE5B;EACAnE,SAAS,CAAC,MAAM;IACd,IAAImE,WAAW,CAACsD,MAAM,GAAG,CAAC,IAAIpD,iBAAiB,GAAGF,WAAW,CAACsD,MAAM,EAAE;MACpExF,iBAAiB,CAACkC,WAAW,CAACE,iBAAiB,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,CAACA,iBAAiB,EAAEF,WAAW,CAAC,CAAC;;EAEpC;EACAnE,SAAS,CAAC,MAAM;IACd,IAAI8B,YAAY,EAAE;MAChB,MAAM4F,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC7CF,KAAK,CAACG,GAAG,GAAG/F,YAAY;MACxB4F,KAAK,CAACI,OAAO,GAAG,UAAU;MAC1BJ,KAAK,CAACK,gBAAgB,GAAG,MAAM;QAC7B5B,gBAAgB,CAACuB,KAAK,CAACM,QAAQ,CAAC;QAChC;QACA,IAAIN,KAAK,CAACO,2BAA2B,KAAKC,SAAS,EAAE;UACnD;UACA,IAAI;YACF,MAAMC,MAAM,GAAGT,KAAK,CAACU,WAAW,IAAKV,KAAK,CAACW,aAAa,IAAIX,KAAK,CAACW,aAAa,CAAC,CAAC,CAACC,cAAc,CAAC,CAAE;YACnG,IAAIH,MAAM,IAAIA,MAAM,CAACV,MAAM,GAAG,CAAC,IAAIU,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,EAAE;cACxD,MAAMC,QAAQ,GAAGL,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;cACxC,IAAIC,QAAQ,CAACC,SAAS,EAAE;gBACtBpC,WAAW,CAACmC,QAAQ,CAACC,SAAS,CAAC;cACjC;YACF;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;QACf;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAAC5G,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM6G,oBAAoB,GAAGzC,aAAa,IAAIE,QAAQ,GAAGwC,IAAI,CAACC,KAAK,CAAC3C,aAAa,GAAGE,QAAQ,CAAC,GAAG,IAAI;;EAEpG;EACApG,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIqF,SAAS,CAAC8B,OAAO,EAAE;QACrB9B,SAAS,CAAC8B,OAAO,CAAC2B,KAAK,CAAC,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAMmJ,iBAAiB,GAAIP,CAAC,IAAK;IAC/B,MAAMQ,IAAI,GAAGR,CAAC,CAACS,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR;MACA,MAAMG,UAAU,GAAGrI,kBAAkB,CAACkI,IAAI,EAAE,OAAO,EAAE,wBAAwB,CAAC;MAC9E,IAAI,CAACG,UAAU,CAACC,OAAO,EAAE;QACvBrI,uBAAuB,CAACoI,UAAU,CAACE,YAAY,EAAElH,QAAQ,CAAC;QAC1D;QACA,IAAIqG,CAAC,CAACS,MAAM,EAAE;UACZT,CAAC,CAACS,MAAM,CAAC5C,KAAK,GAAG,EAAE;QACrB;QACA;MACF;;MAEA;MACAlE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMqF,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC7CF,KAAK,CAACI,OAAO,GAAG,UAAU;MAC1BJ,KAAK,CAACK,gBAAgB,GAAG,MAAM;QAC7B,IAAIL,KAAK,CAACM,QAAQ,GAAG,EAAE,EAAE;UACvBgB,UAAU,CAAC,wEAAwE,CAAC;UACpFnH,YAAY,CAAC,IAAI,CAAC;UAClBE,eAAe,CAAC,IAAI,CAAC;UACrBE,iBAAiB,CAAC,IAAI,CAAC;UACvB+C,eAAe,CAAC,IAAI,CAAC;UACrBF,gBAAgB,CAAC,EAAE,CAAC;UACpBzC,QAAQ,CAAC,EAAE,CAAC;UACZ,IAAIkD,YAAY,CAAC4B,OAAO,EAAE5B,YAAY,CAAC4B,OAAO,CAACZ,KAAK,GAAG,EAAE;QAC3D,CAAC,MAAM;UACLyC,UAAU,CAAC,EAAE,CAAC;UACdnH,YAAY,CAACqH,IAAI,CAAC;UAClBnH,eAAe,CAACyH,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC,CAAC;UAC1CjH,iBAAiB,CAAC,IAAI,CAAC;UACvB+C,eAAe,CAAC,IAAI,CAAC;UACrBF,gBAAgB,CAAC,EAAE,CAAC;UACpBzC,QAAQ,CAAC,EAAE,CAAC;QACd;MACF,CAAC;MACDqF,KAAK,CAACG,GAAG,GAAG2B,GAAG,CAACC,eAAe,CAACP,IAAI,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB3G,eAAe,CAAC,CAACD,YAAY,CAAC;IAC9B,IAAI,CAACA,YAAY,EAAE;MACjBjB,YAAY,CAAC,IAAI,CAAC;MAClBE,eAAe,CAAC,IAAI,CAAC;MACrBE,iBAAiB,CAAC,IAAI,CAAC;MACvB+C,eAAe,CAAC,IAAI,CAAC;MACrBF,gBAAgB,CAAC,EAAE,CAAC;MACpBzC,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMsH,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCzC,OAAO,CAAC0C,GAAG,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACtE,SAAS,CAAC6B,OAAO,IAAI,CAAC7B,SAAS,CAAC6B,OAAO,CAAC0C,MAAM,EAAE;MACnDxH,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,IAAI;MACFkB,iBAAiB,CAAC,EAAE,CAAC;MACrBC,iBAAiB,CAAC2D,OAAO,GAAG,EAAE;MAC9B9D,gBAAgB,CAAC,CAAC,CAAC;MACnBF,cAAc,CAAC,IAAI,CAAC;MACpBd,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMyH,aAAa,GAAG,IAAIC,aAAa,CAACzE,SAAS,CAAC6B,OAAO,CAAC0C,MAAM,EAAE;QAChEG,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFvE,gBAAgB,CAAC0B,OAAO,GAAG2C,aAAa;MAExCA,aAAa,CAACG,eAAe,GAAIC,KAAK,IAAK;QACzChD,OAAO,CAAC0C,GAAG,CAAC,8BAA8B,EAAEM,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC;QAC5D,IAAIF,KAAK,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;UACvB7G,iBAAiB,CAAC8D,IAAI,IAAI;YACxB,MAAMgD,OAAO,GAAG,CAAC,GAAGhD,IAAI,EAAE6C,KAAK,CAACC,IAAI,CAAC;YACrC3G,iBAAiB,CAAC2D,OAAO,GAAGkD,OAAO,CAAC,CAAC;YACrC,OAAOA,OAAO;UAChB,CAAC,CAAC;QACJ;MACF,CAAC;MAEDP,aAAa,CAACQ,MAAM,GAAG,MAAM;QAC3BpD,OAAO,CAAC0C,GAAG,CAAC,4BAA4B,CAAC;QACzC;QACA,MAAMW,MAAM,GAAG/G,iBAAiB,CAAC2D,OAAO;QACxC;QACAD,OAAO,CAAC0C,GAAG,CAAC,gCAAgC,EAAEW,MAAM,CAAC9C,MAAM,CAAC;QAC5D,IAAI+C,SAAS,GAAG,CAAC;QACjBD,MAAM,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACvBzD,OAAO,CAAC0C,GAAG,CAAC,SAASe,CAAC,QAAQ,EAAED,CAAC,CAACN,IAAI,CAAC;UACvCI,SAAS,IAAIE,CAAC,CAACN,IAAI;QACrB,CAAC,CAAC;QACFlD,OAAO,CAAC0C,GAAG,CAAC,sBAAsB,EAAEY,SAAS,CAAC;QAC9C,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAACN,MAAM,EAAE;UAAEO,IAAI,EAAE;QAAa,CAAC,CAAC;QACrD,MAAM5B,IAAI,GAAG,IAAI6B,IAAI,CAAC,CAACH,IAAI,CAAC,EAAE,kBAAkBI,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO,EAAE;UAAEH,IAAI,EAAE;QAAa,CAAC,CAAC;QAC1FjJ,YAAY,CAACqH,IAAI,CAAC;QAClBnH,eAAe,CAACyH,GAAG,CAACC,eAAe,CAACmB,IAAI,CAAC,CAAC;QAC1CzH,cAAc,CAAC,KAAK,CAAC;QACrBE,gBAAgB,CAAC,CAAC,CAAC;QACnB;QACAG,iBAAiB,CAAC2D,OAAO,GAAG,EAAE;QAC9B5D,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC;MAEDuG,aAAa,CAACoB,OAAO,GAAG,MAAM;QAC5BhE,OAAO,CAAC0C,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC;MACDE,aAAa,CAACqB,OAAO,GAAIzC,CAAC,IAAK;QAC7BxB,OAAO,CAAC9E,KAAK,CAAC,uBAAuB,EAAEsG,CAAC,CAAC;MAC3C,CAAC;MAEDxB,OAAO,CAAC0C,GAAG,CAAC,mCAAmC,CAAC;MAChDE,aAAa,CAACsB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAOhJ,KAAK,EAAE;MACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAACiJ,OAAO,CAAC;MACvDlI,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMmE,mBAAmB,GAAGrH,WAAW,CAAC,MAAM;IAC5CiH,OAAO,CAAC0C,GAAG,CAAC,4BAA4B,CAAC;IACzC,IAAInE,gBAAgB,CAAC0B,OAAO,IAAIjE,WAAW,EAAE;MAC3CuC,gBAAgB,CAAC0B,OAAO,CAACmE,IAAI,CAAC,CAAC;MAC/BnI,cAAc,CAAC,KAAK,CAAC;MACrBE,gBAAgB,CAAC,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMqI,uBAAuB,GAAGA,CAAA,KAAM;IACpCtI,oBAAoB,CAACoE,IAAI,IAAIA,IAAI,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,CAAC;EAC/E,CAAC;;EAED;EACA,MAAMmE,oBAAoB,GAAGA,CAAA,KAAM;IACjC,OAAQ5I,WAAW,KAAK,OAAO,IAAIhB,SAAS,IACpCgB,WAAW,KAAK,QAAQ,IAAIhB,SAAU;EAChD,CAAC;;EAED;EACA,MAAM6J,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACD,oBAAoB,CAAC,CAAC,EAAE;MAC3BnJ,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;;IAEA;IACAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC,KAAK,CAAC;IACpB+B,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,KAAK,CAAC;IACnBN,cAAc,CAAC,EAAE,CAAC;IAClBE,oBAAoB,CAAC,CAAC,CAAC;IACvBM,qBAAqB,CAAC,CAAC,CAAC;IACxBE,gBAAgB,CAAC,EAAE,CAAC;IACpBI,oBAAoB,CAAC,EAAE,CAAC;IACxBjD,iBAAiB,CAAC,IAAI,CAAC;IACvB+C,eAAe,CAAC,IAAI,CAAC;IACrBI,cAAc,CAAC,KAAK,CAAC;;IAErB;IACAC,SAAS,CAAC8B,OAAO,GAAG,IAAIuE,eAAe,CAAC,CAAC;IAEzC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEjK,SAAS,CAAC;MACnC+J,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEnK,aAAa,CAAC;MAC/CiK,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEnJ,WAAW,CAAC;MAC3C,MAAMoJ,UAAU,GAAGC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;MACjD,MAAMC,IAAI,GAAGH,UAAU,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI;MACvDH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,QAAQ,KAAI,SAAS,CAAC;MACxDT,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,CAAAI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,KAAI,SAAS,CAAC;MAEhDnF,OAAO,CAAC0C,GAAG,CAAC,uCAAuC,EAAElI,aAAa,CAAC;MAEnE,MAAM4K,MAAM,GAAG,4BAA4B;MAE3C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,EAAE;QACnCG,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEf,QAAQ;QACdgB,MAAM,EAAEtH,SAAS,CAAC8B,OAAO,CAACwF;MAC5B,CAAC,CAAC;MAEF,IAAI,CAACJ,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBN,QAAQ,CAACO,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMC,MAAM,GAAGR,QAAQ,CAACG,IAAI,CAACM,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MACjC,IAAIC,MAAM,GAAG,EAAE;;MAEf;MACA,MAAMC,gBAAgB,GAAIC,UAAU,IAAK;QACvC,IAAIA,UAAU,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,IAAIA,UAAU,CAAC5F,MAAM,GAAG,CAAC,EAAE;UACpE3C,gBAAgB,CAACuC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGgG,UAAU,CAAC,CAAC;QACpD;MACF,CAAC;MAED,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,OAAO,IAAI,EAAE;YACX,MAAM;cAAEC,IAAI;cAAElH;YAAM,CAAC,GAAG,MAAMwG,MAAM,CAACW,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACRvG,OAAO,CAAC0C,GAAG,CAAC,wBAAwB,CAAC;cACrCrH,eAAe,CAAC,KAAK,CAAC;cACtBJ,UAAU,CAAC,KAAK,CAAC;cACjBqC,cAAc,CAAC,KAAK,CAAC;cACrB;YACF;YAEA2I,MAAM,IAAIF,OAAO,CAACU,MAAM,CAACpH,KAAK,EAAE;cAAEsD,MAAM,EAAE;YAAK,CAAC,CAAC;YACjD,IAAI+D,KAAK,GAAGT,MAAM,CAACU,KAAK,CAAC,IAAI,CAAC;YAC9B;YACAV,MAAM,GAAGS,KAAK,CAACE,GAAG,CAAC,CAAC;YAEpB,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;cACxB,IAAIG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC7B,IAAI;kBACF,MAAM7D,IAAI,GAAG+B,IAAI,CAACC,KAAK,CAAC4B,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;kBAC1C;kBACA/G,OAAO,CAAC0C,GAAG,CAAC,WAAW,EAAEO,IAAI,CAAC;;kBAE9B;kBACA,IAAIA,IAAI,CAAC+D,OAAO,KAAK,KAAK,EAAE;oBAC1B7L,QAAQ,CAAC8H,IAAI,CAACkB,OAAO,IAAI,yBAAyB,CAAC;oBACnD9I,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjBqC,cAAc,CAAC,KAAK,CAAC;oBACrB;kBACF;;kBAEA;kBACA,IAAI2F,IAAI,CAACgE,QAAQ,KAAKjG,SAAS,IAAInC,gBAAgB,EAAE;oBACnDnB,qBAAqB,CAACuF,IAAI,CAACgE,QAAQ,CAAC;oBACpC,IAAI,CAAChJ,WAAW,IAAIgF,IAAI,CAACgE,QAAQ,GAAG,CAAC,EAAE;sBACrC/I,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAC,MAAM,IAAI+E,IAAI,CAACiE,WAAW,KAAKlG,SAAS,IAAInC,gBAAgB,EAAE;oBAC7D;oBACA,MAAMoI,QAAQ,GAAIhE,IAAI,CAACiE,WAAW,GAAGvI,WAAW,GAAI,GAAG;oBACvDjB,qBAAqB,CAACuJ,QAAQ,CAAC;oBAC/B,IAAI,CAAChJ,WAAW,IAAIgJ,QAAQ,GAAG,CAAC,EAAE;sBAChC/I,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF,CAAC,MAAM,IAAI+E,IAAI,CAACiE,WAAW,KAAKlG,SAAS,IAAIS,oBAAoB,EAAE;oBACjE;oBACA,MAAMwF,QAAQ,GAAIhE,IAAI,CAACiE,WAAW,GAAGzF,oBAAoB,GAAI,GAAG;oBAChE/D,qBAAqB,CAACuJ,QAAQ,CAAC;oBAC/B,IAAI,CAAChJ,WAAW,IAAIgJ,QAAQ,GAAG,CAAC,EAAE;sBAChC/I,cAAc,CAAC,IAAI,CAAC;oBACtB;kBACF;;kBAEA;kBACA,IAAI+E,IAAI,CAACkE,KAAK,IAAI,OAAOlE,IAAI,CAACkE,KAAK,KAAK,QAAQ,IAAIlE,IAAI,CAACkE,KAAK,CAAC5G,MAAM,GAAG,IAAI,EAAE;oBAC5ErD,cAAc,CAACiD,IAAI,IAAI;sBACrB,MAAMiH,SAAS,GAAG,CAAC,GAAGjH,IAAI,EAAE8C,IAAI,CAACkE,KAAK,CAAC;sBACvC,OAAOC,SAAS;oBAClB,CAAC,CAAC;oBACFrM,iBAAiB,CAACkI,IAAI,CAACkE,KAAK,CAAC;oBAC7B/J,oBAAoB,CAAC+C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;oBACtC,IAAI9C,WAAW,EAAE;sBACfC,cAAc,CAAC,KAAK,CAAC;oBACvB;kBACF;;kBAEA;kBACA,IAAI2F,IAAI,CAACkD,UAAU,IAAIlD,IAAI,CAACkD,UAAU,CAAC5F,MAAM,GAAG,CAAC,EAAE;oBACjDvC,oBAAoB,CAACiF,IAAI,CAACkD,UAAU,CAAC;oBACrCD,gBAAgB,CAACjD,IAAI,CAACkD,UAAU,CAAC;kBACnC;;kBAEA;kBACA,IAAIlD,IAAI,CAACoE,cAAc,EAAE;oBACvBvJ,eAAe,CAACmF,IAAI,CAAC;oBACrBrF,gBAAgB,CAACqF,IAAI,CAACoE,cAAc,CAAC;oBACrChM,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjBqC,cAAc,CAAC,KAAK,CAAC;oBACrBI,qBAAqB,CAAC,GAAG,CAAC;oBAC1BN,oBAAoB,CAAC,CAAC,CAAC;oBACvBI,YAAY,CAAC,KAAK,CAAC;oBACnBwC,OAAO,CAAC0C,GAAG,CAAC,4BAA4B,CAAC;oBACzC;kBACF;;kBAEA;kBACA,IAAIO,IAAI,CAACqE,GAAG,EAAE;oBACZtH,OAAO,CAAC0C,GAAG,CAAC,qBAAqB,CAAC;oBAClCrH,eAAe,CAAC,KAAK,CAAC;oBACtBJ,UAAU,CAAC,KAAK,CAAC;oBACjBqC,cAAc,CAAC,KAAK,CAAC;oBACrB;kBACF;;kBAEA;kBACA,IAAI2F,IAAI,CAACsE,YAAY,KAAKvG,SAAS,EAAE;oBACnCpC,cAAc,CAACqE,IAAI,CAACsE,YAAY,CAAC;kBACnC;gBACF,CAAC,CAAC,OAAOC,UAAU,EAAE;kBACnBxH,OAAO,CAACyH,IAAI,CAAC,yBAAyB,EAAED,UAAU,CAAC;gBACrD;cACF;YACF;UACF;QACF,CAAC,CAAC,OAAOE,WAAW,EAAE;UACpB,IAAIA,WAAW,CAACC,IAAI,KAAK,YAAY,EAAE;YACrC3H,OAAO,CAAC0C,GAAG,CAAC,wBAAwB,CAAC;UACvC,CAAC,MAAM;YACL1C,OAAO,CAAC9E,KAAK,CAAC,0BAA0B,EAAEwM,WAAW,CAAC;YACtDvM,QAAQ,CAAC,+BAA+B,CAAC;UAC3C;UACAE,eAAe,CAAC,KAAK,CAAC;UACtBJ,UAAU,CAAC,KAAK,CAAC;UACjBqC,cAAc,CAAC,KAAK,CAAC;QACvB,CAAC,SAAS;UACR,IAAIuI,MAAM,EAAE;YACV,IAAI;cACFA,MAAM,CAAC+B,WAAW,CAAC,CAAC;YACtB,CAAC,CAAC,OAAOpG,CAAC,EAAE;cACVxB,OAAO,CAACyH,IAAI,CAAC,8BAA8B,EAAEjG,CAAC,CAAC;YACjD;UACF;QACF;MACF,CAAC;MAED,MAAM8E,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOpL,KAAK,EAAE;MACd8E,OAAO,CAAC9E,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAACiJ,OAAO,IAAI,yBAAyB,CAAC;MACpDlJ,UAAU,CAAC,KAAK,CAAC;MACjBI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMwM,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMlO,KAAK,CAACmO,IAAI,CAAC,qCAAqC,CAAC;MAEvDzM,eAAe,CAAC,KAAK,CAAC;MACtBE,aAAa,CAAC,IAAI,CAAC;MACnB+B,cAAc,CAAC,KAAK,CAAC;MACrBE,YAAY,CAAC,KAAK,CAAC;MACnBvC,UAAU,CAAC,KAAK,CAAC;MACjBE,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd8E,OAAO,CAAC9E,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,2BAA2B,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAM4M,WAAW,GAAGA,CAAA,KAAM;IACxBpN,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvB+C,eAAe,CAAC,IAAI,CAAC;IACrBF,gBAAgB,CAAC,EAAE,CAAC;IACpBI,oBAAoB,CAAC,EAAE,CAAC;IACxBd,cAAc,CAAC,EAAE,CAAC;IAClBE,oBAAoB,CAAC,CAAC,CAAC;IACvB/B,eAAe,CAAC,KAAK,CAAC;IACtBE,aAAa,CAAC,KAAK,CAAC;IACpB+B,cAAc,CAAC,KAAK,CAAC;IACrBE,YAAY,CAAC,KAAK,CAAC;IACnBE,qBAAqB,CAAC,CAAC,CAAC;IACxBvC,QAAQ,CAAC,EAAE,CAAC;IACZV,gBAAgB,CAAC,KAAK,CAAC;IACvByD,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;IACvB,IAAIG,YAAY,CAAC4B,OAAO,EAAE;MACxB5B,YAAY,CAAC4B,OAAO,CAACZ,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA,MAAM2I,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClBtK,aAAa,CAAC4F,OAAO,CAAC2E,GAAG,IAAI;MAC3BD,OAAO,CAACC,GAAG,CAACtE,IAAI,CAAC,GAAG,CAACqE,OAAO,CAACC,GAAG,CAACtE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAClD,CAAC,CAAC;IACF,OAAOqE,OAAO;EAChB,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAItK,YAAY,EAAE;MAChB,OAAO;QACLuK,gBAAgB,EAAEvK,YAAY,CAACwK,uBAAuB,IAAI1K,aAAa,CAAC4C,MAAM;QAC9E+H,eAAe,EAAEzK,YAAY,CAAC0K,sBAAsB,IAAI5K,aAAa,CAAC4C,MAAM;QAC5EiI,iBAAiB,EAAE,CAAC3K,YAAY,CAAC0K,sBAAsB,IAAI5K,aAAa,CAAC4C,MAAM,KAAK1C,YAAY,CAACwK,uBAAuB,IAAI1K,aAAa,CAAC4C,MAAM;MAClJ,CAAC;IACH;IACA,OAAO;MACL6H,gBAAgB,EAAEzK,aAAa,CAAC4C,MAAM;MACtC+H,eAAe,EAAE3K,aAAa,CAAC4C,MAAM;MACrCiI,iBAAiB,EAAE;IACrB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGjH,IAAI,CAACkH,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMG,IAAI,GAAGH,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,IAAIE,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACtD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClCvM,eAAe,CAACuM,KAAK,CAAC;IACtB,IAAIA,KAAK,EAAE;MACTjM,kBAAkB,CAAC,IAAI,CAAC;MACxBgD,OAAO,CAAC0C,GAAG,CAAC,6BAA6B,EAAEuG,KAAK,CAAC;IACnD,CAAC,MAAM;MACLjM,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,MAAMkM,mBAAmB,GAAIC,SAAS,IAAK;IACzCvM,kBAAkB,CAACuM,SAAS,CAAC;IAC7BnJ,OAAO,CAAC0C,GAAG,CAAC,sBAAsB,EAAEyG,SAAS,CAAC;EAChD,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxM,kBAAkB,CAAC,IAAI,CAAC;IACxBoD,OAAO,CAAC0C,GAAG,CAAC,qBAAqB,CAAC;EACpC,CAAC;EAED,MAAM2G,oBAAoB,GAAIC,KAAK,IAAK;IACtCxM,kBAAkB,CAACqD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmJ,KAAK,CAAC,CAAC;;IAE5C;IACA,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACzC,IAAID,YAAY,CAAC,uBAAuB,EAAE;QACxC/D,IAAI,EAAE8D,KAAK,CAACnF,OAAO;QACnBsF,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IAEAzJ,OAAO,CAACyH,IAAI,CAAC,qBAAqB,EAAE6B,KAAK,CAAC;EAC5C,CAAC;;EAED;EACAxQ,SAAS,CAAC,MAAM;IACd,IAAI,cAAc,IAAI4Q,MAAM,IAAIH,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrED,YAAY,CAACI,iBAAiB,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExP,OAAA;IAAKyP,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrC1P,OAAA,CAACb,GAAG;MAAAuQ,QAAA,gBACF1P,OAAA,CAACZ,GAAG;QAACuQ,EAAE,EAAE,CAAE;QAAAD,QAAA,eACT1P,OAAA,CAACnB,IAAI;UAAC4Q,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACpB1P,OAAA,CAACnB,IAAI,CAAC+Q,MAAM;YAACH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAC5C1P,OAAA;cAAKyP,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAChE1P,OAAA;gBAAIyP,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAsB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC/CpN,eAAe,IAAIN,YAAY,iBAC9BtC,OAAA,CAACT,KAAK;gBAAC0Q,EAAE,EAAC,SAAS;gBAAAP,QAAA,EAAC;cAEpB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACR;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACdhQ,OAAA,CAACnB,IAAI,CAACqR,IAAI;YAAAR,QAAA,gBAER1P,OAAA,CAACV,IAAI;cACH6Q,SAAS,EAAE/N,SAAU;cACrBgO,QAAQ,EAAGC,CAAC,IAAKhO,YAAY,CAACgO,CAAC,CAAE;cACjCZ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhB1P,OAAA,CAACX,GAAG;gBAACiR,QAAQ,EAAC,WAAW;gBAACC,KAAK,EAAC;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEtC,CAAC,eACNhQ,OAAA,CAACX,GAAG;gBAACiR,QAAQ,EAAC,OAAO;gBAACC,KAAK,EAAC;cAAgB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEvC,CAAC,eACNhQ,OAAA,CAACX,GAAG;gBAACiR,QAAQ,EAAC,UAAU;gBAACC,KAAK,EAAC;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGN5N,SAAS,KAAK,WAAW,iBACxBpC,OAAA;cAAA0P,QAAA,GACD3O,KAAK,iBACJf,OAAA,CAAChB,KAAK;gBAACwR,OAAO,EAAC,QAAQ;gBAACf,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACrC3O;cAAK;gBAAA8O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR,EACAtI,OAAO,iBACN1H,OAAA,CAAChB,KAAK;gBAACwR,OAAO,EAAC,SAAS;gBAACf,SAAS,EAAC,MAAM;gBAAAC,QAAA,EACtChI;cAAO;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACR,eAGDhQ,OAAA,CAACjB,IAAI,CAAC0R,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1P,OAAA,CAACjB,IAAI,CAAC2R,KAAK;kBAAAhB,QAAA,EAAC;gBAAe;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxChQ,OAAA,CAACjB,IAAI,CAAC4R,MAAM;kBACVzL,KAAK,EAAE7E,aAAc;kBACrBuQ,QAAQ,EAAGvJ,CAAC,IAAK/G,gBAAgB,CAAC+G,CAAC,CAACS,MAAM,CAAC5C,KAAK,CAAE;kBAClD2L,QAAQ,EAAE5P,YAAa;kBAAAyO,QAAA,EAEtBzK,YAAY,CAAC6L,GAAG,CAACC,MAAM,iBACtB/Q,OAAA;oBAA2BkF,KAAK,EAAE6L,MAAM,CAAC7L,KAAM;oBAAAwK,QAAA,EAC5CqB,MAAM,CAAC5L;kBAAK,GADF4L,MAAM,CAAC7L,KAAK;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGbhQ,OAAA,CAACjB,IAAI,CAAC0R,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1P,OAAA,CAACjB,IAAI,CAAC2R,KAAK;kBAAAhB,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrChQ,OAAA,CAACjB,IAAI,CAAC4R,MAAM;kBACVzL,KAAK,EAAE3D,WAAY;kBACnBqP,QAAQ,EAAGvJ,CAAC,IAAK7F,cAAc,CAAC6F,CAAC,CAACS,MAAM,CAAC5C,KAAK,CAAE;kBAChD2L,QAAQ,EAAE5P,YAAa;kBAAAyO,QAAA,gBAEvB1P,OAAA;oBAAQkF,KAAK,EAAC,OAAO;oBAAAwK,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3ChQ,OAAA;oBAAQkF,KAAK,EAAC,QAAQ;oBAAAwK,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAGZzO,WAAW,KAAK,OAAO,iBACtBvB,OAAA,CAACjB,IAAI,CAAC0R,KAAK;gBAAChB,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1P,OAAA,CAACjB,IAAI,CAAC2R,KAAK;kBAAAhB,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrChQ,OAAA,CAACjB,IAAI,CAACiS,OAAO;kBACXvH,IAAI,EAAC,MAAM;kBACXwH,MAAM,EAAC,SAAS;kBAChBL,QAAQ,EAAEhJ,iBAAkB;kBAC5BsJ,GAAG,EAAEhN,YAAa;kBAClB2M,QAAQ,EAAE5P;gBAAa;kBAAA4O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,EACDvP,YAAY,iBACXT,OAAA;kBAAKyP,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB1P,OAAA;oBACEwG,GAAG,EAAE/F,YAAa;oBAClB0Q,QAAQ;oBACR1B,SAAS,EAAC,eAAe;oBACzB2B,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAQ;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CACb,EAGAzO,WAAW,KAAK,QAAQ,iBACvBvB,OAAA;gBAAKyP,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1P,OAAA;kBAAKyP,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC1P,OAAA,CAAClB,MAAM;oBACL0R,OAAO,EAAE/O,YAAY,GAAG,QAAQ,GAAG,MAAO;oBAC1C6P,OAAO,EAAEjJ,YAAa;oBACtBwI,QAAQ,EAAE5P,YAAa;oBAAAyO,QAAA,EAEtBjO,YAAY,GAAG,aAAa,GAAG;kBAAc;oBAAAoO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,EACR3L,QAAQ,IAAI5C,YAAY,iBACvBzB,OAAA,CAAClB,MAAM;oBACL0R,OAAO,EAAC,mBAAmB;oBAC3Bc,OAAO,EAAEpH,uBAAwB;oBACjCnB,IAAI,EAAC,IAAI;oBAAA2G,QAAA,EACV;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAELvO,YAAY,iBACXzB,OAAA;kBAAKyP,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B1P,OAAA,CAACP,MAAM;oBACL8R,KAAK,EAAE,KAAM;oBACbL,GAAG,EAAEjN,SAAU;oBACfuN,gBAAgB,EAAC,YAAY;oBAC7BC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,MAAM;oBACbC,gBAAgB,EAAE;sBAChBF,KAAK,EAAE,GAAG;sBACVC,MAAM,EAAE,GAAG;sBACXE,UAAU,EAAEjQ;oBACd;kBAAE;oBAAAkO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEFhQ,OAAA;oBAAKyP,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAClB,CAAC7N,WAAW,gBACX7B,OAAA,CAAClB,MAAM;sBACL0R,OAAO,EAAC,SAAS;sBACjBc,OAAO,EAAEhJ,oBAAqB;sBAC9BuI,QAAQ,EAAE5P,YAAa;sBAAAyO,QAAA,EACxB;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,gBAEThQ,OAAA;sBAAKyP,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C1P,OAAA,CAAClB,MAAM;wBACL0R,OAAO,EAAC,QAAQ;wBAChBc,OAAO,EAAErL,mBAAoB;wBAAAyJ,QAAA,EAC9B;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACThQ,OAAA;wBAAMyP,SAAS,EAAC,aAAa;wBAAAC,QAAA,GAAC,aACjB,EAACpB,UAAU,CAACvM,aAAa,CAAC,EAAC,KAAG,EAACuM,UAAU,CAAC/J,kBAAkB,CAAC;sBAAA;wBAAAsL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAvP,YAAY,iBACXT,OAAA;kBAAKyP,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB1P,OAAA;oBACEwG,GAAG,EAAE/F,YAAa;oBAClB0Q,QAAQ;oBACR1B,SAAS,EAAC,eAAe;oBACzB2B,KAAK,EAAE;sBAAEC,SAAS,EAAE;oBAAQ;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,eAGDhQ,OAAA;gBAAKyP,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B1P,OAAA,CAAClB,MAAM;kBACL0R,OAAO,EAAC,SAAS;kBACjBc,OAAO,EAAElH,aAAc;kBACvByG,QAAQ,EAAE,CAAC1G,oBAAoB,CAAC,CAAC,IAAIlJ,YAAa;kBAAAyO,QAAA,EAEjD7O,OAAO,gBACNb,OAAA,CAAAE,SAAA;oBAAAwP,QAAA,gBACE1P,OAAA,CAACf,OAAO;sBAAC8J,IAAI,EAAC,IAAI;sBAAC0G,SAAS,EAAC;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,iBAExC;kBAAA,eAAE,CAAC,GAEH;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,EAER/O,YAAY,iBACXjB,OAAA,CAAClB,MAAM;kBACL0R,OAAO,EAAC,SAAS;kBACjBc,OAAO,EAAE5D,oBAAqB;kBAAAgC,QAAA,EAC/B;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eAEDhQ,OAAA,CAAClB,MAAM;kBACL0R,OAAO,EAAC,WAAW;kBACnBc,OAAO,EAAE1D,WAAY;kBACrBiD,QAAQ,EAAE5P,YAAa;kBAAAyO,QAAA,EACxB;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAGL/O,YAAY,iBACXjB,OAAA;gBAAKyP,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAClB,CAAChL,gBAAgB,IAAI4C,oBAAoB,KAAKhE,kBAAkB,GAAG,CAAC,IAAIqB,MAAM,CAACC,QAAQ,CAACtB,kBAAkB,CAAC,gBAC1GtD,OAAA,CAAAE,SAAA;kBAAAwP,QAAA,gBACE1P,OAAA;oBAAKyP,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7C1P,OAAA;sBAAA0P,QAAA,EAAM;oBAAoB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjChQ,OAAA;sBAAA0P,QAAA,GAAOnI,IAAI,CAACsK,GAAG,CAAC,CAAC,EAAEtK,IAAI,CAACuK,GAAG,CAAC,GAAG,EAAExO,kBAAkB,CAAC,CAAC,CAACqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAkK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACNhQ,OAAA;oBAAKyP,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5B1P,OAAA;sBACEyP,SAAS,EAAC,yDAAyD;sBACnEzE,IAAI,EAAC,aAAa;sBAClBoG,KAAK,EAAE;wBAAEK,KAAK,EAAE,GAAGlK,IAAI,CAACsK,GAAG,CAAC,CAAC,EAAEtK,IAAI,CAACuK,GAAG,CAAC,GAAG,EAAExO,kBAAkB,CAAC,CAAC;sBAAI,CAAE;sBACvE,iBAAeiE,IAAI,CAACsK,GAAG,CAAC,CAAC,EAAEtK,IAAI,CAACuK,GAAG,CAAC,GAAG,EAAExO,kBAAkB,CAAC,CAAE;sBAC9D,iBAAc,GAAG;sBACjB,iBAAc;oBAAK;sBAAAuM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,eACN,CAAC,gBAEHhQ,OAAA;kBAAKyP,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7C1P,OAAA;oBAAA0P,QAAA,EAAM;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1BhQ,OAAA;oBAAKyP,SAAS,EAAC,2BAA2B;oBAAC2B,KAAK,EAAE;sBAAEM,MAAM,EAAE;oBAAO,CAAE;oBAAAhC,QAAA,eACnE1P,OAAA;sBACEyP,SAAS,EAAC,yDAAyD;sBACnEzE,IAAI,EAAC,aAAa;sBAClBoG,KAAK,EAAE;wBAAEK,KAAK,EAAE,MAAM;wBAAEM,eAAe,EAAE;sBAAU,CAAE;sBACrD,iBAAe,CAAE;sBACjB,iBAAc,GAAG;sBACjB,iBAAc;oBAAK;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAGA5N,SAAS,KAAK,OAAO,iBACpBpC,OAAA;gBAAA0P,QAAA,eACE1P,OAAA,CAACH,YAAY;kBACXmS,YAAY,EAAEnD,gBAAiB;kBAC/BgC,QAAQ,EAAE5P;gBAAa;kBAAA4O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGA5N,SAAS,KAAK,UAAU,iBACvBpC,OAAA;gBAAA0P,QAAA,EACGpN,YAAY,gBACXtC,OAAA,CAACF,YAAY;kBACXgP,KAAK,EAAExM,YAAa;kBACpB2P,eAAe,EAAElD,mBAAoB;kBACrCmD,cAAc,EAAEjD,kBAAmB;kBACnCkD,gBAAgB,EAAEjD,oBAAqB;kBACvCkD,QAAQ,EAAEvQ,WAAW,IAAIZ,YAAa;kBACtC4P,QAAQ,EAAE,CAACjO;gBAAgB;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,gBAEFhQ,OAAA,CAAChB,KAAK;kBAACwR,OAAO,EAAC,MAAM;kBAAAd,QAAA,eACnB1P,OAAA;oBAAKyP,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1B1P,OAAA;sBAAA0P,QAAA,EAAI;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzBhQ,OAAA;sBAAA0P,QAAA,EAAG;oBAAqF;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC5FhQ,OAAA,CAAClB,MAAM;sBACL0R,OAAO,EAAC,SAAS;sBACjBc,OAAO,EAAEA,CAAA,KAAMjP,YAAY,CAAC,OAAO,CAAE;sBAAAqN,QAAA,EACtC;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEI,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENhQ,OAAA,CAACZ,GAAG;QAACuQ,EAAE,EAAE,CAAE;QAAAD,QAAA,EAER,CAAE5L,WAAW,IAAIN,aAAa,CAAC4C,MAAM,GAAG,CAAC,IAAK,CAACnF,YAAY,IAAIyC,YAAY,IAAIF,aAAa,CAAC4C,MAAM,GAAG,CAAE,kBACvGpG,OAAA,CAACnB,IAAI;UAAA6Q,QAAA,gBACH1P,OAAA,CAACnB,IAAI,CAAC+Q,MAAM;YAACH,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACzC1P,OAAA;cAAIyP,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC1C/O,YAAY,iBACXjB,OAAA;cAAOyP,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAEjC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,EACA,CAAC/O,YAAY,IAAIyC,YAAY,iBAC5B1D,OAAA;cAAOyP,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7B1P,OAAA;gBAAA0P,QAAA,EAAG;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,mCAC7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdhQ,OAAA,CAACnB,IAAI,CAACqR,IAAI;YAAAR,QAAA,gBAER1P,OAAA;cAAKyP,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC1P,OAAA;gBAAA0P,QAAA,EAAI;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BhQ,OAAA;gBAAKyP,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAClB2C,MAAM,CAACC,OAAO,CAACzE,mBAAmB,CAAC,CAAC,CAAC,CAACiD,GAAG,CAAC,CAAC,CAACrH,IAAI,EAAE8I,KAAK,CAAC,kBACvDvS,OAAA;kBAAiByP,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GACjDjG,IAAI,EAAC,IAAE,EAAC8I,KAAK;gBAAA,GADL9I,IAAI;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAET,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNhQ,OAAA;gBAAKyP,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B1P,OAAA;kBAAOyP,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBAC3B1P,OAAA;oBAAA0P,QAAA,EAAQ;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC,GAAG,eACrChQ,OAAA;oBAAMyP,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAC,UAC9B,EAAC1B,gBAAgB,CAAC,CAAC,CAACC,gBAAgB;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACPhQ,OAAA;oBAAMyP,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,GAAC,gBACrB,EAAC1B,gBAAgB,CAAC,CAAC,CAACG,eAAe;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACPhQ,OAAA;oBAAMyP,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,GAAC,sBACb,EAAC1B,gBAAgB,CAAC,CAAC,CAACK,iBAAiB;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAAC,MAAM;cACN,MAAMwC,iBAAiB,GAAGhP,aAAa,CAACiP,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjJ,IAAI,KAAK,SAAS,CAAC;cACzE,MAAMkJ,eAAe,GAAGnP,aAAa,CAACiP,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjJ,IAAI,CAACmJ,QAAQ,CAAC,OAAO,CAAC,CAAC;cAC3E,MAAMC,cAAc,GAAGrP,aAAa,CAACiP,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjJ,IAAI,CAACmJ,QAAQ,CAAC,MAAM,CAAC,CAAC;cAEzE,oBACE5S,OAAA;gBAAA0P,QAAA,GAEG,CAACrP,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,UAAU,kBACvDL,OAAA;kBAAKyP,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3C1P,OAAA;oBAAIyP,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBACzB1P,OAAA;sBAAMyP,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,uBACf,EAACwC,iBAAiB,CAACpM,MAAM;kBAAA;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,EACJwC,iBAAiB,CAACpM,MAAM,GAAG,CAAC,gBAC3BpG,OAAA;oBAAKyP,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxC1P,OAAA,CAACd,KAAK;sBAAC4T,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAACjK,IAAI,EAAC,IAAI;sBAAA2G,QAAA,gBACrC1P,OAAA;wBAAA0P,QAAA,eACE1P,OAAA;0BAAA0P,QAAA,gBACE1P,OAAA;4BAAA0P,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACrBhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAY;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRhQ,OAAA;wBAAA0P,QAAA,EACG8C,iBAAiB,CAAC1B,GAAG,CAAC,CAACmC,SAAS,EAAEC,KAAK,kBACtClT,OAAA;0BAAA0P,QAAA,gBACE1P,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACE,QAAQ,IAAID,KAAK,GAAG;0BAAC;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1ChQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACG,QAAQ,CAACzN,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrEhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACI,QAAQ,GAAGJ,SAAS,CAACI,QAAQ,CAAC1N,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrEhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACK,MAAM,GAAGL,SAAS,CAACK,MAAM,CAAC3N,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACjEhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACM,YAAY,IAAI;0BAAK;4BAAA1D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GALnCkD,KAAK;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAMV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENhQ,OAAA;oBAAKyP,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EAGA,CAAC3P,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,kBAAkB,kBAC/DL,OAAA;kBAAKyP,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC1P,OAAA;oBAAIyP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC1B1P,OAAA;sBAAMyP,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,qBAChB,EAAC2C,eAAe,CAACvM,MAAM;kBAAA;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,EACJ2C,eAAe,CAACvM,MAAM,GAAG,CAAC,gBACzBpG,OAAA;oBAAKyP,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxC1P,OAAA,CAACd,KAAK;sBAAC4T,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAACjK,IAAI,EAAC,IAAI;sBAAA2G,QAAA,gBACrC1P,OAAA;wBAAA0P,QAAA,eACE1P,OAAA;0BAAA0P,QAAA,gBACE1P,OAAA;4BAAA0P,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAI;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRhQ,OAAA;wBAAA0P,QAAA,EACGiD,eAAe,CAAC7B,GAAG,CAAC,CAACmC,SAAS,EAAEC,KAAK,kBACpClT,OAAA;0BAAA0P,QAAA,gBACE1P,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACE,QAAQ,IAAID,KAAK,GAAG;0BAAC;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1ChQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACxJ;0BAAI;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACzBhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACG,QAAQ,CAACzN,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrEhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACO,UAAU,IAAI;0BAAK;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAJjCkD,KAAK;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAKV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENhQ,OAAA;oBAAKyP,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC5D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN,EAGA,CAAC3P,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,OAAO,kBACpDL,OAAA;kBAAKyP,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxC1P,OAAA;oBAAIyP,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC1B1P,OAAA;sBAAMyP,SAAS,EAAC,OAAO;sBAAAC,QAAA,EAAC;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,oBACjB,EAAC6C,cAAc,CAACzM,MAAM;kBAAA;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACJ6C,cAAc,CAACzM,MAAM,GAAG,CAAC,gBACxBpG,OAAA;oBAAKyP,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,eACxC1P,OAAA,CAACd,KAAK;sBAAC4T,OAAO;sBAACC,QAAQ;sBAACC,KAAK;sBAACjK,IAAI,EAAC,IAAI;sBAAA2G,QAAA,gBACrC1P,OAAA;wBAAA0P,QAAA,eACE1P,OAAA;0BAAA0P,QAAA,gBACE1P,OAAA;4BAAA0P,QAAA,EAAI;0BAAE;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACXhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAI;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAS;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAClBhQ,OAAA;4BAAA0P,QAAA,EAAI;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRhQ,OAAA;wBAAA0P,QAAA,EACGmD,cAAc,CAAC/B,GAAG,CAAC,CAACmC,SAAS,EAAEC,KAAK,kBACnClT,OAAA;0BAAA0P,QAAA,gBACE1P,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACE,QAAQ,IAAID,KAAK,GAAG;0BAAC;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1ChQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACQ,SAAS,IAAI;0BAAe;4BAAA5D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACjDhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACS,SAAS,IAAIT,SAAS,CAACxJ;0BAAI;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAChDhQ,OAAA;4BAAA0P,QAAA,EAAKuD,SAAS,CAACU,QAAQ,GAAGV,SAAS,CAACU,QAAQ,CAAChO,OAAO,CAAC,CAAC,CAAC,GAAG;0BAAK;4BAAAkK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA,GAJ9DkD,KAAK;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAKV,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENhQ,OAAA;oBAAKyP,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAC3D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAEV,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5P,EAAA,CA9kCID,oBAAoB;EAAA,QA2CHT,aAAa;AAAA;AAAAkU,EAAA,GA3C9BzT,oBAAoB;AAglC1B,eAAeA,oBAAoB;AAAC,IAAAyT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}