{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\components\\\\RoutePlanner.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, Alert, Spinner, Badge, Form } from 'react-bootstrap';\nimport { FaRoute, FaClock, FaRoad, FaMapMarkerAlt, FaExchangeAlt } from 'react-icons/fa';\nimport LocationPicker from './LocationPicker';\nimport RouteMap from './RouteMap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoutePlanner = ({\n  onRouteReady,\n  disabled = false,\n  initialPickup = null,\n  initialDrop = null\n}) => {\n  _s();\n  const [pickupLocation, setPickupLocation] = useState(initialPickup);\n  const [dropLocation, setDropLocation] = useState(initialDrop);\n  const [route, setRoute] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [routingProfile, setRoutingProfile] = useState('driving');\n\n  // Auto-generate route when both locations are selected\n  useEffect(() => {\n    if (pickupLocation && dropLocation && !disabled) {\n      generateRoute();\n    } else {\n      setRoute(null);\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    }\n  }, [pickupLocation, dropLocation, routingProfile, disabled]);\n  const generateRoute = async () => {\n    if (!pickupLocation || !dropLocation) return;\n    setLoading(true);\n    setError('');\n    try {\n      console.log('🗺️ Generating route...', {\n        pickupLocation,\n        dropLocation,\n        routingProfile\n      });\n      const response = await fetch('/api/location/route', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          start: {\n            latitude: pickupLocation.latitude,\n            longitude: pickupLocation.longitude\n          },\n          end: {\n            latitude: dropLocation.latitude,\n            longitude: dropLocation.longitude\n          },\n          profile: routingProfile\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const routeData = {\n            ...data.result,\n            pickup: pickupLocation,\n            drop: dropLocation,\n            profile: routingProfile\n          };\n          setRoute(routeData);\n          if (onRouteReady) {\n            onRouteReady(routeData);\n          }\n          console.log('✅ Route generated successfully:', routeData);\n        } else {\n          throw new Error(data.error || 'Failed to generate route');\n        }\n      } else {\n        throw new Error(`HTTP ${response.status}: Failed to generate route`);\n      }\n    } catch (error) {\n      console.error('❌ Route generation failed:', error);\n      setError(`Route generation failed: ${error.message}`);\n      setRoute(null);\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSwapLocations = () => {\n    if (disabled) return;\n    const temp = pickupLocation;\n    setPickupLocation(dropLocation);\n    setDropLocation(temp);\n  };\n  const formatDuration = minutes => {\n    if (minutes < 60) {\n      return `${Math.round(minutes)} min`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.round(minutes % 60);\n      return `${hours}h ${mins}m`;\n    }\n  };\n  const formatDistance = km => {\n    if (km < 1) {\n      return `${Math.round(km * 1000)} m`;\n    } else {\n      return `${km.toFixed(1)} km`;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"route-planner\",\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: \"Route Planning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Transportation Mode\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n          value: routingProfile,\n          onChange: e => setRoutingProfile(e.target.value),\n          disabled: disabled,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"driving\",\n            children: \"\\uD83D\\uDE97 Driving\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"walking\",\n            children: \"\\uD83D\\uDEB6 Walking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"cycling\",\n            children: \"\\uD83D\\uDEB4 Cycling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(LocationPicker, {\n            label: \"Pickup Location (From)\",\n            placeholder: \"Enter pickup address or use auto-detect\",\n            onLocationSelect: setPickupLocation,\n            allowAutoDetect: true,\n            allowManualEntry: true,\n            initialLocation: pickupLocation,\n            disabled: disabled\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(LocationPicker, {\n            label: \"Drop Location (To)\",\n            placeholder: \"Enter destination address\",\n            onLocationSelect: setDropLocation,\n            allowAutoDetect: false,\n            allowManualEntry: true,\n            initialLocation: dropLocation,\n            disabled: disabled\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), pickupLocation && dropLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          size: \"sm\",\n          onClick: handleSwapLocations,\n          disabled: disabled,\n          title: \"Swap pickup and drop locations\",\n          children: [/*#__PURE__*/_jsxDEV(FaExchangeAlt, {\n            className: \"me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), \"Swap Locations\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(Spinner, {\n          animation: \"border\",\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Generating route...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"mb-3\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), route && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"route-info\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"success\",\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Route Generated Successfully\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-3\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaRoad, {\n                  className: \"me-2 text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-bold\",\n                    children: formatDistance(route.distance_km)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Distance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaClock, {\n                  className: \"me-2 text-info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-bold\",\n                    children: formatDuration(route.duration_minutes)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n                  className: \"me-2 text-success\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fw-bold\",\n                    children: route.coordinates.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Route Points\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Badge, {\n                  bg: \"secondary\",\n                  className: \"me-2\",\n                  children: route.source.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Routing Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), route.instructions && route.instructions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"route-instructions\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-2\",\n            children: \"Route Instructions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"instructions-list\",\n            style: {\n              maxHeight: '200px',\n              overflowY: 'auto'\n            },\n            children: route.instructions.map((instruction, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instruction-item p-2 border-bottom\",\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"light\",\n                text: \"dark\",\n                className: \"me-2\",\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 23\n              }, this), instruction]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), route && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: /*#__PURE__*/_jsxDEV(RouteMap, {\n          route: route,\n          pickup: pickupLocation,\n          drop: dropLocation,\n          height: \"300px\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this), route && !loading && !error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"info\",\n        className: \"mt-3 mb-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(FaRoute, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Route Ready!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"small\",\n              children: \"You can now start video recording with live tracking enabled. The system will monitor your path and alert you of any deviations from this planned route.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(RoutePlanner, \"6lwPXeiR+lR1GIdI13QUnEJN6MU=\");\n_c = RoutePlanner;\nexport default RoutePlanner;\nvar _c;\n$RefreshReg$(_c, \"RoutePlanner\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Badge", "Form", "FaRoute", "FaClock", "FaRoad", "FaMapMarkerAlt", "FaExchangeAlt", "LocationPicker", "RouteMap", "jsxDEV", "_jsxDEV", "Route<PERSON><PERSON>ner", "onRouteReady", "disabled", "initialPickup", "initialDrop", "_s", "pickupLocation", "setPickupLocation", "dropLocation", "setDropLocation", "route", "setRoute", "loading", "setLoading", "error", "setError", "routingProfile", "setRoutingProfile", "generateRoute", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "start", "latitude", "longitude", "end", "profile", "ok", "data", "json", "success", "routeData", "result", "pickup", "drop", "Error", "status", "message", "handleSwapLocations", "temp", "formatDuration", "minutes", "Math", "round", "hours", "floor", "mins", "formatDistance", "km", "toFixed", "className", "children", "Header", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "Group", "Label", "Select", "value", "onChange", "e", "target", "md", "label", "placeholder", "onLocationSelect", "allowAutoDetect", "allowManualEntry", "initialLocation", "variant", "size", "onClick", "title", "animation", "sm", "distance_km", "duration_minutes", "coordinates", "length", "bg", "source", "toUpperCase", "instructions", "style", "maxHeight", "overflowY", "map", "instruction", "index", "text", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/components/RoutePlanner.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, <PERSON>ert, <PERSON>, Badge, Form } from 'react-bootstrap';\nimport { FaRoute, FaClock, FaRoad, FaMapMarkerAlt, FaExchangeAlt } from 'react-icons/fa';\nimport LocationPicker from './LocationPicker';\nimport RouteMap from './RouteMap';\n\nconst RoutePlanner = ({ onRouteReady, disabled = false, initialPickup = null, initialDrop = null }) => {\n  const [pickupLocation, setPickupLocation] = useState(initialPickup);\n  const [dropLocation, setDropLocation] = useState(initialDrop);\n  const [route, setRoute] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [routingProfile, setRoutingProfile] = useState('driving');\n\n  // Auto-generate route when both locations are selected\n  useEffect(() => {\n    if (pickupLocation && dropLocation && !disabled) {\n      generateRoute();\n    } else {\n      setRoute(null);\n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    }\n  }, [pickupLocation, dropLocation, routingProfile, disabled]);\n\n  const generateRoute = async () => {\n    if (!pickupLocation || !dropLocation) return;\n    \n    setLoading(true);\n    setError('');\n    \n    try {\n      console.log('🗺️ Generating route...', { pickupLocation, dropLocation, routingProfile });\n      \n      const response = await fetch('/api/location/route', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          start: {\n            latitude: pickupLocation.latitude,\n            longitude: pickupLocation.longitude\n          },\n          end: {\n            latitude: dropLocation.latitude,\n            longitude: dropLocation.longitude\n          },\n          profile: routingProfile\n        })\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          const routeData = {\n            ...data.result,\n            pickup: pickupLocation,\n            drop: dropLocation,\n            profile: routingProfile\n          };\n          \n          setRoute(routeData);\n          \n          if (onRouteReady) {\n            onRouteReady(routeData);\n          }\n          \n          console.log('✅ Route generated successfully:', routeData);\n        } else {\n          throw new Error(data.error || 'Failed to generate route');\n        }\n      } else {\n        throw new Error(`HTTP ${response.status}: Failed to generate route`);\n      }\n    } catch (error) {\n      console.error('❌ Route generation failed:', error);\n      setError(`Route generation failed: ${error.message}`);\n      setRoute(null);\n      \n      if (onRouteReady) {\n        onRouteReady(null);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSwapLocations = () => {\n    if (disabled) return;\n    \n    const temp = pickupLocation;\n    setPickupLocation(dropLocation);\n    setDropLocation(temp);\n  };\n\n  const formatDuration = (minutes) => {\n    if (minutes < 60) {\n      return `${Math.round(minutes)} min`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.round(minutes % 60);\n      return `${hours}h ${mins}m`;\n    }\n  };\n\n  const formatDistance = (km) => {\n    if (km < 1) {\n      return `${Math.round(km * 1000)} m`;\n    } else {\n      return `${km.toFixed(1)} km`;\n    }\n  };\n\n  return (\n    <Card className=\"route-planner\">\n      <Card.Header className=\"bg-primary text-white\">\n        <div className=\"d-flex align-items-center\">\n          <FaRoute className=\"me-2\" />\n          <h5 className=\"mb-0\">Route Planning</h5>\n        </div>\n      </Card.Header>\n      \n      <Card.Body>\n        {/* Routing Profile Selection */}\n        <Form.Group className=\"mb-3\">\n          <Form.Label>Transportation Mode</Form.Label>\n          <Form.Select\n            value={routingProfile}\n            onChange={(e) => setRoutingProfile(e.target.value)}\n            disabled={disabled}\n          >\n            <option value=\"driving\">🚗 Driving</option>\n            <option value=\"walking\">🚶 Walking</option>\n            <option value=\"cycling\">🚴 Cycling</option>\n          </Form.Select>\n        </Form.Group>\n\n        <Row>\n          <Col md={6}>\n            {/* Pickup Location */}\n            <LocationPicker\n              label=\"Pickup Location (From)\"\n              placeholder=\"Enter pickup address or use auto-detect\"\n              onLocationSelect={setPickupLocation}\n              allowAutoDetect={true}\n              allowManualEntry={true}\n              initialLocation={pickupLocation}\n              disabled={disabled}\n            />\n          </Col>\n          \n          <Col md={6}>\n            {/* Drop Location */}\n            <LocationPicker\n              label=\"Drop Location (To)\"\n              placeholder=\"Enter destination address\"\n              onLocationSelect={setDropLocation}\n              allowAutoDetect={false}\n              allowManualEntry={true}\n              initialLocation={dropLocation}\n              disabled={disabled}\n            />\n          </Col>\n        </Row>\n\n        {/* Swap Locations Button */}\n        {pickupLocation && dropLocation && (\n          <div className=\"text-center mb-3\">\n            <Button\n              variant=\"outline-secondary\"\n              size=\"sm\"\n              onClick={handleSwapLocations}\n              disabled={disabled}\n              title=\"Swap pickup and drop locations\"\n            >\n              <FaExchangeAlt className=\"me-1\" />\n              Swap Locations\n            </Button>\n          </div>\n        )}\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"text-center py-3\">\n            <Spinner animation=\"border\" className=\"me-2\" />\n            <span>Generating route...</span>\n          </div>\n        )}\n\n        {/* Error Display */}\n        {error && (\n          <Alert variant=\"danger\" className=\"mb-3\">\n            {error}\n          </Alert>\n        )}\n\n        {/* Route Information */}\n        {route && !loading && (\n          <div className=\"route-info\">\n            <Alert variant=\"success\" className=\"mb-3\">\n              <div className=\"d-flex align-items-center mb-2\">\n                <FaRoute className=\"me-2\" />\n                <strong>Route Generated Successfully</strong>\n              </div>\n              \n              <Row className=\"g-3\">\n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <FaRoad className=\"me-2 text-primary\" />\n                    <div>\n                      <div className=\"fw-bold\">{formatDistance(route.distance_km)}</div>\n                      <small className=\"text-muted\">Distance</small>\n                    </div>\n                  </div>\n                </Col>\n                \n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <FaClock className=\"me-2 text-info\" />\n                    <div>\n                      <div className=\"fw-bold\">{formatDuration(route.duration_minutes)}</div>\n                      <small className=\"text-muted\">Duration</small>\n                    </div>\n                  </div>\n                </Col>\n                \n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <FaMapMarkerAlt className=\"me-2 text-success\" />\n                    <div>\n                      <div className=\"fw-bold\">{route.coordinates.length}</div>\n                      <small className=\"text-muted\">Route Points</small>\n                    </div>\n                  </div>\n                </Col>\n                \n                <Col sm={6} md={3}>\n                  <div className=\"d-flex align-items-center\">\n                    <Badge bg=\"secondary\" className=\"me-2\">\n                      {route.source.toUpperCase()}\n                    </Badge>\n                    <div>\n                      <small className=\"text-muted\">Routing Service</small>\n                    </div>\n                  </div>\n                </Col>\n              </Row>\n            </Alert>\n\n            {/* Route Instructions */}\n            {route.instructions && route.instructions.length > 0 && (\n              <div className=\"route-instructions\">\n                <h6 className=\"mb-2\">Route Instructions</h6>\n                <div className=\"instructions-list\" style={{ maxHeight: '200px', overflowY: 'auto' }}>\n                  {route.instructions.map((instruction, index) => (\n                    <div key={index} className=\"instruction-item p-2 border-bottom\">\n                      <Badge bg=\"light\" text=\"dark\" className=\"me-2\">\n                        {index + 1}\n                      </Badge>\n                      {instruction}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Route Map */}\n        {route && (\n          <div className=\"mt-3\">\n            <RouteMap\n              route={route}\n              pickup={pickupLocation}\n              drop={dropLocation}\n              height=\"300px\"\n            />\n          </div>\n        )}\n\n        {/* Ready State */}\n        {route && !loading && !error && (\n          <Alert variant=\"info\" className=\"mt-3 mb-0\">\n            <div className=\"d-flex align-items-center\">\n              <FaRoute className=\"me-2\" />\n              <div>\n                <strong>Route Ready!</strong>\n                <div className=\"small\">\n                  You can now start video recording with live tracking enabled.\n                  The system will monitor your path and alert you of any deviations from this planned route.\n                </div>\n              </div>\n            </div>\n          </Alert>\n        )}\n      </Card.Body>\n    </Card>\n  );\n};\n\nexport default RoutePlanner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AACrF,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,cAAc,EAAEC,aAAa,QAAQ,gBAAgB;AACxF,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,YAAY;EAAEC,QAAQ,GAAG,KAAK;EAAEC,aAAa,GAAG,IAAI;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrG,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAACsB,aAAa,CAAC;EACnE,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAACuB,WAAW,CAAC;EAC7D,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,SAAS,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIwB,cAAc,IAAIE,YAAY,IAAI,CAACN,QAAQ,EAAE;MAC/CgB,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACLP,QAAQ,CAAC,IAAI,CAAC;MACd,IAAIV,YAAY,EAAE;QAChBA,YAAY,CAAC,IAAI,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACK,cAAc,EAAEE,YAAY,EAAEQ,cAAc,EAAEd,QAAQ,CAAC,CAAC;EAE5D,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACZ,cAAc,IAAI,CAACE,YAAY,EAAE;IAEtCK,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACFI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;QAAEd,cAAc;QAAEE,YAAY;QAAEQ;MAAe,CAAC,CAAC;MAExF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB,EAAE;QAClDC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE;YACLC,QAAQ,EAAEvB,cAAc,CAACuB,QAAQ;YACjCC,SAAS,EAAExB,cAAc,CAACwB;UAC5B,CAAC;UACDC,GAAG,EAAE;YACHF,QAAQ,EAAErB,YAAY,CAACqB,QAAQ;YAC/BC,SAAS,EAAEtB,YAAY,CAACsB;UAC1B,CAAC;UACDE,OAAO,EAAEhB;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAIK,QAAQ,CAACY,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;QAClC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChB,MAAMC,SAAS,GAAG;YAChB,GAAGH,IAAI,CAACI,MAAM;YACdC,MAAM,EAAEjC,cAAc;YACtBkC,IAAI,EAAEhC,YAAY;YAClBwB,OAAO,EAAEhB;UACX,CAAC;UAEDL,QAAQ,CAAC0B,SAAS,CAAC;UAEnB,IAAIpC,YAAY,EAAE;YAChBA,YAAY,CAACoC,SAAS,CAAC;UACzB;UAEAlB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEiB,SAAS,CAAC;QAC3D,CAAC,MAAM;UACL,MAAM,IAAII,KAAK,CAACP,IAAI,CAACpB,KAAK,IAAI,0BAA0B,CAAC;QAC3D;MACF,CAAC,MAAM;QACL,MAAM,IAAI2B,KAAK,CAAC,QAAQpB,QAAQ,CAACqB,MAAM,4BAA4B,CAAC;MACtE;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,4BAA4BD,KAAK,CAAC6B,OAAO,EAAE,CAAC;MACrDhC,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIV,YAAY,EAAE;QAChBA,YAAY,CAAC,IAAI,CAAC;MACpB;IACF,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI1C,QAAQ,EAAE;IAEd,MAAM2C,IAAI,GAAGvC,cAAc;IAC3BC,iBAAiB,CAACC,YAAY,CAAC;IAC/BC,eAAe,CAACoC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE;MAChB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC,MAAM;IACrC,CAAC,MAAM;MACL,MAAMG,KAAK,GAAGF,IAAI,CAACG,KAAK,CAACJ,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMK,IAAI,GAAGJ,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;MACrC,OAAO,GAAGG,KAAK,KAAKE,IAAI,GAAG;IAC7B;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,EAAE,IAAK;IAC7B,IAAIA,EAAE,GAAG,CAAC,EAAE;MACV,OAAO,GAAGN,IAAI,CAACC,KAAK,CAACK,EAAE,GAAG,IAAI,CAAC,IAAI;IACrC,CAAC,MAAM;MACL,OAAO,GAAGA,EAAE,CAACC,OAAO,CAAC,CAAC,CAAC,KAAK;IAC9B;EACF,CAAC;EAED,oBACExD,OAAA,CAAChB,IAAI;IAACyE,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC7B1D,OAAA,CAAChB,IAAI,CAAC2E,MAAM;MAACF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAC5C1D,OAAA;QAAKyD,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxC1D,OAAA,CAACR,OAAO;UAACiE,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B/D,OAAA;UAAIyD,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd/D,OAAA,CAAChB,IAAI,CAACgF,IAAI;MAAAN,QAAA,gBAER1D,OAAA,CAACT,IAAI,CAAC0E,KAAK;QAACR,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAC1B1D,OAAA,CAACT,IAAI,CAAC2E,KAAK;UAAAR,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5C/D,OAAA,CAACT,IAAI,CAAC4E,MAAM;UACVC,KAAK,EAAEnD,cAAe;UACtBoD,QAAQ,EAAGC,CAAC,IAAKpD,iBAAiB,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACnDjE,QAAQ,EAAEA,QAAS;UAAAuD,QAAA,gBAEnB1D,OAAA;YAAQoE,KAAK,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3C/D,OAAA;YAAQoE,KAAK,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3C/D,OAAA;YAAQoE,KAAK,EAAC,SAAS;YAAAV,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEb/D,OAAA,CAACf,GAAG;QAAAyE,QAAA,gBACF1D,OAAA,CAACd,GAAG;UAACsF,EAAE,EAAE,CAAE;UAAAd,QAAA,eAET1D,OAAA,CAACH,cAAc;YACb4E,KAAK,EAAC,wBAAwB;YAC9BC,WAAW,EAAC,yCAAyC;YACrDC,gBAAgB,EAAEnE,iBAAkB;YACpCoE,eAAe,EAAE,IAAK;YACtBC,gBAAgB,EAAE,IAAK;YACvBC,eAAe,EAAEvE,cAAe;YAChCJ,QAAQ,EAAEA;UAAS;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN/D,OAAA,CAACd,GAAG;UAACsF,EAAE,EAAE,CAAE;UAAAd,QAAA,eAET1D,OAAA,CAACH,cAAc;YACb4E,KAAK,EAAC,oBAAoB;YAC1BC,WAAW,EAAC,2BAA2B;YACvCC,gBAAgB,EAAEjE,eAAgB;YAClCkE,eAAe,EAAE,KAAM;YACvBC,gBAAgB,EAAE,IAAK;YACvBC,eAAe,EAAErE,YAAa;YAC9BN,QAAQ,EAAEA;UAAS;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxD,cAAc,IAAIE,YAAY,iBAC7BT,OAAA;QAAKyD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B1D,OAAA,CAACb,MAAM;UACL4F,OAAO,EAAC,mBAAmB;UAC3BC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEpC,mBAAoB;UAC7B1C,QAAQ,EAAEA,QAAS;UACnB+E,KAAK,EAAC,gCAAgC;UAAAxB,QAAA,gBAEtC1D,OAAA,CAACJ,aAAa;YAAC6D,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAlD,OAAO,iBACNb,OAAA;QAAKyD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1D,OAAA,CAACX,OAAO;UAAC8F,SAAS,EAAC,QAAQ;UAAC1B,SAAS,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/C/D,OAAA;UAAA0D,QAAA,EAAM;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACN,EAGAhD,KAAK,iBACJf,OAAA,CAACZ,KAAK;QAAC2F,OAAO,EAAC,QAAQ;QAACtB,SAAS,EAAC,MAAM;QAAAC,QAAA,EACrC3C;MAAK;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGApD,KAAK,IAAI,CAACE,OAAO,iBAChBb,OAAA;QAAKyD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB1D,OAAA,CAACZ,KAAK;UAAC2F,OAAO,EAAC,SAAS;UAACtB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACvC1D,OAAA;YAAKyD,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C1D,OAAA,CAACR,OAAO;cAACiE,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B/D,OAAA;cAAA0D,QAAA,EAAQ;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAEN/D,OAAA,CAACf,GAAG;YAACwE,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB1D,OAAA,CAACd,GAAG;cAACkG,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChB1D,OAAA;gBAAKyD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC1D,OAAA,CAACN,MAAM;kBAAC+D,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxC/D,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAKyD,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEJ,cAAc,CAAC3C,KAAK,CAAC0E,WAAW;kBAAC;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE/D,OAAA;oBAAOyD,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/D,OAAA,CAACd,GAAG;cAACkG,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChB1D,OAAA;gBAAKyD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC1D,OAAA,CAACP,OAAO;kBAACgE,SAAS,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtC/D,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAKyD,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAEX,cAAc,CAACpC,KAAK,CAAC2E,gBAAgB;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE/D,OAAA;oBAAOyD,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/D,OAAA,CAACd,GAAG;cAACkG,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChB1D,OAAA;gBAAKyD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC1D,OAAA,CAACL,cAAc;kBAAC8D,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChD/D,OAAA;kBAAA0D,QAAA,gBACE1D,OAAA;oBAAKyD,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAE/C,KAAK,CAAC4E,WAAW,CAACC;kBAAM;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzD/D,OAAA;oBAAOyD,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/D,OAAA,CAACd,GAAG;cAACkG,EAAE,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAd,QAAA,eAChB1D,OAAA;gBAAKyD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxC1D,OAAA,CAACV,KAAK;kBAACmG,EAAE,EAAC,WAAW;kBAAChC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EACnC/C,KAAK,CAAC+E,MAAM,CAACC,WAAW,CAAC;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACR/D,OAAA;kBAAA0D,QAAA,eACE1D,OAAA;oBAAOyD,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGPpD,KAAK,CAACiF,YAAY,IAAIjF,KAAK,CAACiF,YAAY,CAACJ,MAAM,GAAG,CAAC,iBAClDxF,OAAA;UAAKyD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC1D,OAAA;YAAIyD,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C/D,OAAA;YAAKyD,SAAS,EAAC,mBAAmB;YAACoC,KAAK,EAAE;cAAEC,SAAS,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAArC,QAAA,EACjF/C,KAAK,CAACiF,YAAY,CAACI,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACzClG,OAAA;cAAiByD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBAC7D1D,OAAA,CAACV,KAAK;gBAACmG,EAAE,EAAC,OAAO;gBAACU,IAAI,EAAC,MAAM;gBAAC1C,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAC3CwC,KAAK,GAAG;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACPkC,WAAW;YAAA,GAJJC,KAAK;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGApD,KAAK,iBACJX,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB1D,OAAA,CAACF,QAAQ;UACPa,KAAK,EAAEA,KAAM;UACb6B,MAAM,EAAEjC,cAAe;UACvBkC,IAAI,EAAEhC,YAAa;UACnB2F,MAAM,EAAC;QAAO;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGApD,KAAK,IAAI,CAACE,OAAO,IAAI,CAACE,KAAK,iBAC1Bf,OAAA,CAACZ,KAAK;QAAC2F,OAAO,EAAC,MAAM;QAACtB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACzC1D,OAAA;UAAKyD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC1D,OAAA,CAACR,OAAO;YAACiE,SAAS,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B/D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAA0D,QAAA,EAAQ;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7B/D,OAAA;cAAKyD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAGvB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX,CAAC;AAACzD,EAAA,CAtSIL,YAAY;AAAAoG,EAAA,GAAZpG,YAAY;AAwSlB,eAAeA,YAAY;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}